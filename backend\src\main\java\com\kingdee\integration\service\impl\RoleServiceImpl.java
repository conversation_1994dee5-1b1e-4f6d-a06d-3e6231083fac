package com.kingdee.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kingdee.integration.entity.Role;
import com.kingdee.integration.entity.Permission;
import com.kingdee.integration.mapper.RoleMapper;
import com.kingdee.integration.service.RoleService;
import com.kingdee.integration.service.PermissionService;
import com.kingdee.integration.exception.BusinessException;
import com.kingdee.integration.enums.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 角色服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    private final PermissionService permissionService;

    @Override
    @Cacheable(value = "role", key = "#roleCode")
    public Role getByRoleCode(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return null;
        }
        return baseMapper.selectByRoleCode(roleCode);
    }

    @Override
    @Cacheable(value = "role", key = "#roleName")
    public Role getByRoleName(String roleName) {
        if (!StringUtils.hasText(roleName)) {
            return null;
        }
        return baseMapper.selectByRoleName(roleName);
    }

    @Override
    public IPage<Role> pageRoles(Page<Role> page, String roleName, String roleCode, 
                                String roleType, Integer status, String description) {
        return baseMapper.selectRolePage(page, roleName, roleCode, roleType, status, description);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean createRole(Role role) {
        // 验证角色编码唯一性
        if (isRoleCodeExists(role.getRoleCode())) {
            throw new BusinessException(ErrorCode.ROLE_CODE_EXISTS);
        }
        
        // 验证角色名称唯一性
        if (isRoleNameExists(role.getRoleName())) {
            throw new BusinessException(ErrorCode.ROLE_NAME_EXISTS);
        }
        
        role.setCreateTime(LocalDateTime.now());
        role.setUpdateTime(LocalDateTime.now());
        role.setStatus(1); // 默认启用
        
        boolean result = save(role);
        if (result) {
            log.info("创建角色成功: {}", role.getRoleName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean updateRole(Role role) {
        Role existingRole = getById(role.getId());
        if (existingRole == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        // 如果角色编码发生变化，验证新编码的唯一性
        if (!existingRole.getRoleCode().equals(role.getRoleCode()) && 
            isRoleCodeExists(role.getRoleCode())) {
            throw new BusinessException(ErrorCode.ROLE_CODE_EXISTS);
        }
        
        // 如果角色名称发生变化，验证新名称的唯一性
        if (!existingRole.getRoleName().equals(role.getRoleName()) && 
            isRoleNameExists(role.getRoleName())) {
            throw new BusinessException(ErrorCode.ROLE_NAME_EXISTS);
        }
        
        role.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(role);
        if (result) {
            log.info("更新角色成功: {}", role.getRoleName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean deleteRole(Long roleId) {
        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        // 检查角色是否被用户使用
        Long userCount = baseMapper.getRoleUserCount(roleId);
        if (userCount > 0) {
            throw new BusinessException(ErrorCode.ROLE_IN_USE);
        }
        
        // 删除角色权限关联
        baseMapper.deleteRolePermissions(roleId);
        
        boolean result = removeById(roleId);
        if (result) {
            log.info("删除角色成功: {}", role.getRoleName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean deleteBatchRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return false;
        }
        
        // 检查角色是否被用户使用
        for (Long roleId : roleIds) {
            Long userCount = baseMapper.getRoleUserCount(roleId);
            if (userCount > 0) {
                Role role = getById(roleId);
                throw new BusinessException(ErrorCode.ROLE_IN_USE, 
                    "角色 '" + (role != null ? role.getRoleName() : roleId) + "' 正在被用户使用，无法删除");
            }
        }
        
        // 删除角色权限关联
        for (Long roleId : roleIds) {
            baseMapper.deleteRolePermissions(roleId);
        }
        
        boolean result = removeByIds(roleIds);
        if (result) {
            log.info("批量删除角色成功，数量: {}", roleIds.size());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean enableRole(Long roleId) {
        return updateRoleStatus(roleId, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean disableRole(Long roleId) {
        return updateRoleStatus(roleId, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean assignPermissions(Long roleId, List<Long> permissionIds) {
        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        // 验证权限是否存在
        if (permissionIds != null && !permissionIds.isEmpty()) {
            List<Permission> permissions = permissionService.listByIds(permissionIds);
            if (permissions.size() != permissionIds.size()) {
                throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
            }
        }
        
        // 删除原有权限关联
        baseMapper.deleteRolePermissions(roleId);
        
        // 添加新的权限关联
        if (permissionIds != null && !permissionIds.isEmpty()) {
            baseMapper.insertRolePermissions(roleId, permissionIds);
        }
        
        log.info("角色权限分配成功: {} -> {}", role.getRoleName(), permissionIds);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public boolean removePermissions(Long roleId, List<Long> permissionIds) {
        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        if (permissionIds != null && !permissionIds.isEmpty()) {
            baseMapper.deleteRolePermissionsByIds(roleId, permissionIds);
            log.info("角色权限移除成功: {} -> {}", role.getRoleName(), permissionIds);
        }
        
        return true;
    }

    @Override
    public List<Role> getRolesByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectRolesByUserId(userId);
    }

    @Override
    public List<Role> getRolesByType(String roleType) {
        if (!StringUtils.hasText(roleType)) {
            return Collections.emptyList();
        }
        return baseMapper.selectRolesByType(roleType);
    }

    @Override
    @Cacheable(value = "role", key = "'enabled'")
    public List<Role> getEnabledRoles() {
        return baseMapper.selectEnabledRoles();
    }

    @Override
    public List<Permission> getRolePermissions(Long roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectRolePermissions(roleId);
    }

    @Override
    public boolean hasPermission(Long roleId, String permissionCode) {
        if (roleId == null || !StringUtils.hasText(permissionCode)) {
            return false;
        }
        return baseMapper.hasPermission(roleId, permissionCode);
    }

    @Override
    public boolean isRoleCodeExists(String roleCode) {
        if (!StringUtils.hasText(roleCode)) {
            return false;
        }
        return baseMapper.existsByRoleCode(roleCode);
    }

    @Override
    public boolean isRoleNameExists(String roleName) {
        if (!StringUtils.hasText(roleName)) {
            return false;
        }
        return baseMapper.existsByRoleName(roleName);
    }

    @Override
    public Long countRoles() {
        return baseMapper.countRoles();
    }

    @Override
    public Long countRolesByType(String roleType) {
        if (!StringUtils.hasText(roleType)) {
            return 0L;
        }
        return baseMapper.countRolesByType(roleType);
    }

    @Override
    public Long countRolesByStatus(Integer status) {
        if (status == null) {
            return 0L;
        }
        return baseMapper.countRolesByStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public Role copyRole(Long sourceRoleId, String newRoleCode, String newRoleName) {
        Role sourceRole = getById(sourceRoleId);
        if (sourceRole == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        // 验证新角色编码和名称的唯一性
        if (isRoleCodeExists(newRoleCode)) {
            throw new BusinessException(ErrorCode.ROLE_CODE_EXISTS);
        }
        if (isRoleNameExists(newRoleName)) {
            throw new BusinessException(ErrorCode.ROLE_NAME_EXISTS);
        }
        
        // 创建新角色
        Role newRole = new Role();
        newRole.setRoleCode(newRoleCode);
        newRole.setRoleName(newRoleName);
        newRole.setRoleType(sourceRole.getRoleType());
        newRole.setDescription(sourceRole.getDescription());
        newRole.setStatus(sourceRole.getStatus());
        newRole.setCreateTime(LocalDateTime.now());
        newRole.setUpdateTime(LocalDateTime.now());
        
        save(newRole);
        
        // 复制权限
        List<Permission> sourcePermissions = getRolePermissions(sourceRoleId);
        if (!sourcePermissions.isEmpty()) {
            List<Long> permissionIds = sourcePermissions.stream()
                .map(Permission::getId)
                .collect(Collectors.toList());
            assignPermissions(newRole.getId(), permissionIds);
        }
        
        log.info("角色复制成功: {} -> {}", sourceRole.getRoleName(), newRole.getRoleName());
        return newRole;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "role", allEntries = true)
    public ImportResult importRoles(List<Role> roles) {
        ImportResult result = new ImportResult();
        
        if (roles == null || roles.isEmpty()) {
            return result;
        }
        
        for (Role role : roles) {
            try {
                // 验证必填字段
                if (!StringUtils.hasText(role.getRoleCode()) || 
                    !StringUtils.hasText(role.getRoleName())) {
                    result.addFailure("角色编码和名称不能为空");
                    continue;
                }
                
                // 检查是否已存在
                if (isRoleCodeExists(role.getRoleCode())) {
                    result.addFailure("角色编码已存在: " + role.getRoleCode());
                    continue;
                }
                
                if (isRoleNameExists(role.getRoleName())) {
                    result.addFailure("角色名称已存在: " + role.getRoleName());
                    continue;
                }
                
                // 设置默认值
                role.setCreateTime(LocalDateTime.now());
                role.setUpdateTime(LocalDateTime.now());
                if (role.getStatus() == null) {
                    role.setStatus(1);
                }
                
                save(role);
                result.addSuccess();
                
            } catch (Exception e) {
                log.error("导入角色失败: {}", role.getRoleName(), e);
                result.addFailure("导入失败: " + e.getMessage());
            }
        }
        
        log.info("角色导入完成，成功: {}，失败: {}", result.getSuccessCount(), result.getFailureCount());
        return result;
    }

    @Override
    public List<Role> exportRoles(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return list();
        }
        return listByIds(roleIds);
    }

    @Override
    @CacheEvict(value = "role", allEntries = true)
    public void clearCache() {
        log.info("角色缓存已清空");
    }

    @Override
    @CacheEvict(value = "role", allEntries = true)
    public void syncCache() {
        log.info("角色缓存已同步");
    }

    /**
     * 更新角色状态
     */
    private boolean updateRoleStatus(Long roleId, Integer status) {
        Role role = getById(roleId);
        if (role == null) {
            throw new BusinessException(ErrorCode.ROLE_NOT_FOUND);
        }
        
        boolean result = baseMapper.updateRoleStatus(roleId, status) > 0;
        if (result) {
            String action = status == 1 ? "启用" : "禁用";
            log.info("{}角色成功: {}", action, role.getRoleName());
        }
        return result;
    }
}