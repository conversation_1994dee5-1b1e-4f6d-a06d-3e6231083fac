package com.kingdee.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kingdee.integration.entity.Department;

import java.util.List;
import java.util.Map;

/**
 * 部门服务接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
public interface DepartmentService extends IService<Department> {

    /**
     * 根据部门编码查询部门
     * 
     * @param deptCode 部门编码
     * @return 部门信息
     */
    Department getByDeptCode(String deptCode);

    /**
     * 根据部门名称查询部门
     * 
     * @param deptName 部门名称
     * @return 部门信息
     */
    Department getByDeptName(String deptName);

    /**
     * 分页查询部门列表
     * 
     * @param page 分页参数
     * @param deptName 部门名称
     * @param deptCode 部门编码
     * @param deptType 部门类型
     * @param status 状态
     * @param parentId 父部门ID
     * @return 部门分页列表
     */
    IPage<Department> pageDepartments(Page<Department> page, String deptName, String deptCode,
                                    String deptType, Integer status, Long parentId);

    /**
     * 获取部门树结构
     * 
     * @return 部门树列表
     */
    List<Department> getDepartmentTree();

    /**
     * 获取启用的部门列表
     * 
     * @return 启用的部门列表
     */
    List<Department> getEnabledDepartments();

    /**
     * 根据部门类型查询部门列表
     * 
     * @param deptType 部门类型
     * @return 部门列表
     */
    List<Department> getDepartmentsByType(String deptType);

    /**
     * 根据用户ID查询部门
     * 
     * @param userId 用户ID
     * @return 部门信息
     */
    Department getDepartmentByUserId(Long userId);

    /**
     * 根据负责人ID查询部门列表
     * 
     * @param managerId 负责人ID
     * @return 部门列表
     */
    List<Department> getDepartmentsByManagerId(Long managerId);

    /**
     * 获取子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> getChildDepartments(Long parentId);

    /**
     * 获取所有子部门ID（包括子子部门）
     * 
     * @param parentId 父部门ID
     * @return 子部门ID列表
     */
    List<Long> getAllChildDepartmentIds(Long parentId);

    /**
     * 获取部门路径
     * 
     * @param deptId 部门ID
     * @return 部门路径
     */
    String getDepartmentPath(Long deptId);

    /**
     * 创建部门
     * 
     * @param department 部门信息
     * @return 是否成功
     */
    boolean createDepartment(Department department);

    /**
     * 更新部门
     * 
     * @param department 部门信息
     * @return 是否成功
     */
    boolean updateDepartment(Department department);

    /**
     * 删除部门
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean deleteDepartment(Long deptId);

    /**
     * 批量删除部门
     * 
     * @param deptIds 部门ID列表
     * @return 是否成功
     */
    boolean deleteBatchDepartments(List<Long> deptIds);

    /**
     * 启用部门
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean enableDepartment(Long deptId);

    /**
     * 禁用部门
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean disableDepartment(Long deptId);

    /**
     * 批量更新部门状态
     * 
     * @param deptIds 部门ID列表
     * @param status 状态
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> deptIds, Integer status);

    /**
     * 移动部门
     * 
     * @param deptId 部门ID
     * @param newParentId 新父部门ID
     * @return 是否成功
     */
    boolean moveDepartment(Long deptId, Long newParentId);

    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @return 是否存在
     */
    boolean isDeptCodeExists(String deptCode);

    /**
     * 检查部门名称是否存在
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 是否存在
     */
    boolean isDeptNameExists(String deptName, Long parentId);

    /**
     * 统计部门数量
     * 
     * @return 部门总数
     */
    Long countDepartments();

    /**
     * 根据类型统计部门数量
     * 
     * @param deptType 部门类型
     * @return 部门数量
     */
    Long countDepartmentsByType(String deptType);

    /**
     * 根据状态统计部门数量
     * 
     * @param status 状态
     * @return 部门数量
     */
    Long countDepartmentsByStatus(Integer status);

    /**
     * 获取最大排序号
     * 
     * @param parentId 父部门ID
     * @return 最大排序号
     */
    Integer getMaxSortOrder(Long parentId);

    /**
     * 获取部门详情（包含父部门信息）
     * 
     * @param deptId 部门ID
     * @return 部门详情
     */
    Department getDepartmentWithParent(Long deptId);

    /**
     * 检查部门是否有子部门
     * 
     * @param deptId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildDepartments(Long deptId);

    /**
     * 检查部门是否有用户
     * 
     * @param deptId 部门ID
     * @return 是否有用户
     */
    boolean hasUsers(Long deptId);

    /**
     * 获取部门用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    Long getDepartmentUserCount(Long deptId);

    /**
     * 更新部门路径
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean updateDepartmentPath(Long deptId);

    /**
     * 批量更新子部门路径
     * 
     * @param parentId 父部门ID
     * @return 是否成功
     */
    boolean batchUpdateChildDepartmentPaths(Long parentId);

    /**
     * 更新部门层级
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean updateDepartmentLevel(Long deptId);

    /**
     * 批量更新子部门层级
     * 
     * @param parentId 父部门ID
     * @param levelIncrement 层级增量
     * @return 是否成功
     */
    boolean batchUpdateChildDepartmentLevels(Long parentId, Integer levelIncrement);

    /**
     * 根据部门路径查询部门列表
     * 
     * @param pathPrefix 路径前缀
     * @return 部门列表
     */
    List<Department> getDepartmentsByPathPrefix(String pathPrefix);

    /**
     * 获取根部门列表
     * 
     * @return 根部门列表
     */
    List<Department> getRootDepartments();

    /**
     * 获取叶子部门列表
     * 
     * @return 叶子部门列表
     */
    List<Department> getLeafDepartments();

    /**
     * 根据层级查询部门列表
     * 
     * @param deptLevel 部门层级
     * @return 部门列表
     */
    List<Department> getDepartmentsByLevel(Integer deptLevel);

    /**
     * 获取部门层级结构
     * 
     * @param maxLevel 最大层级
     * @return 部门层级列表
     */
    List<Department> getDepartmentHierarchy(Integer maxLevel);

    /**
     * 搜索部门（支持模糊查询）
     * 
     * @param keyword 关键词
     * @return 部门列表
     */
    List<Department> searchDepartments(String keyword);

    /**
     * 获取用户可访问的部门列表
     * 
     * @param userId 用户ID
     * @return 部门列表
     */
    List<Department> getAccessibleDepartments(Long userId);

    /**
     * 获取部门统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> getDepartmentStatistics();

    /**
     * 复制部门
     * 
     * @param sourceDeptId 源部门ID
     * @param newDeptCode 新部门编码
     * @param newDeptName 新部门名称
     * @param newParentId 新父部门ID
     * @return 新部门信息
     */
    Department copyDepartment(Long sourceDeptId, String newDeptCode, String newDeptName, Long newParentId);

    /**
     * 导入部门
     * 
     * @param departments 部门列表
     * @return 导入结果
     */
    ImportResult importDepartments(List<Department> departments);

    /**
     * 导出部门
     * 
     * @param deptIds 部门ID列表
     * @return 部门列表
     */
    List<Department> exportDepartments(List<Long> deptIds);

    /**
     * 同步部门层级和路径
     * 
     * @return 是否成功
     */
    boolean syncDepartmentHierarchy();

    /**
     * 清空缓存
     */
    void clearCache();

    /**
     * 同步缓存
     */
    void syncCache();

    /**
     * 导入结果内部类
     */
    class ImportResult {
        private int successCount = 0;
        private int failureCount = 0;
        private List<String> errorMessages = new java.util.ArrayList<>();

        public void addSuccess() {
            successCount++;
        }

        public void addFailure(String errorMessage) {
            failureCount++;
            errorMessages.add(errorMessage);
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailureCount() {
            return failureCount;
        }

        public List<String> getErrorMessages() {
            return errorMessages;
        }

        public int getTotalCount() {
            return successCount + failureCount;
        }

        public boolean isAllSuccess() {
            return failureCount == 0;
        }
    }
}