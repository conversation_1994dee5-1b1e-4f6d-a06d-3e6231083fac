package com.kingdee.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kingdee.integration.entity.Department;
import com.kingdee.integration.exception.BusinessException;
import com.kingdee.integration.mapper.DepartmentMapper;
import com.kingdee.integration.service.DepartmentService;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 部门服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class DepartmentServiceImpl extends ServiceImpl<DepartmentMapper, Department> implements DepartmentService {

    @Override
    @Cacheable(value = "department", key = "'code:' + #deptCode")
    public Department getByDeptCode(String deptCode) {
        if (!StringUtils.hasText(deptCode)) {
            return null;
        }
        return baseMapper.selectByDeptCode(deptCode);
    }

    @Override
    @Cacheable(value = "department", key = "'name:' + #deptName")
    public Department getByDeptName(String deptName) {
        if (!StringUtils.hasText(deptName)) {
            return null;
        }
        return baseMapper.selectByDeptName(deptName);
    }

    @Override
    public IPage<Department> pageDepartments(Page<Department> page, String deptName, String deptCode,
                                           String deptType, Integer status, Long parentId) {
        return baseMapper.selectDepartmentPage(page, deptName, deptCode, deptType, status, parentId);
    }

    @Override
    @Cacheable(value = "department", key = "'tree'")
    public List<Department> getDepartmentTree() {
        List<Department> allDepartments = baseMapper.selectDepartmentTree();
        return buildDepartmentTree(allDepartments, null);
    }

    @Override
    @Cacheable(value = "department", key = "'enabled'")
    public List<Department> getEnabledDepartments() {
        return baseMapper.selectEnabledDepartments();
    }

    @Override
    @Cacheable(value = "department", key = "'type:' + #deptType")
    public List<Department> getDepartmentsByType(String deptType) {
        if (!StringUtils.hasText(deptType)) {
            return new ArrayList<>();
        }
        return baseMapper.selectDepartmentsByType(deptType);
    }

    @Override
    @Cacheable(value = "department", key = "'user:' + #userId")
    public Department getDepartmentByUserId(Long userId) {
        if (userId == null) {
            return null;
        }
        return baseMapper.selectDepartmentByUserId(userId);
    }

    @Override
    @Cacheable(value = "department", key = "'manager:' + #managerId")
    public List<Department> getDepartmentsByManagerId(Long managerId) {
        if (managerId == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectDepartmentsByManagerId(managerId);
    }

    @Override
    @Cacheable(value = "department", key = "'children:' + #parentId")
    public List<Department> getChildDepartments(Long parentId) {
        return baseMapper.selectChildDepartments(parentId);
    }

    @Override
    @Cacheable(value = "department", key = "'allChildren:' + #parentId")
    public List<Long> getAllChildDepartmentIds(Long parentId) {
        if (parentId == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectAllChildDepartmentIds(parentId);
    }

    @Override
    @Cacheable(value = "department", key = "'path:' + #deptId")
    public String getDepartmentPath(Long deptId) {
        if (deptId == null) {
            return null;
        }
        return baseMapper.selectDepartmentPath(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean createDepartment(Department department) {
        if (department == null) {
            throw new BusinessException("部门信息不能为空");
        }

        // 验证部门编码唯一性
        if (StringUtils.hasText(department.getDeptCode()) && isDeptCodeExists(department.getDeptCode())) {
            throw new BusinessException("部门编码已存在");
        }

        // 验证部门名称在同一父部门下的唯一性
        if (StringUtils.hasText(department.getDeptName()) && 
            isDeptNameExists(department.getDeptName(), department.getParentId())) {
            throw new BusinessException("同一父部门下部门名称已存在");
        }

        // 设置默认值
        if (department.getStatus() == null) {
            department.setStatus(1); // 默认启用
        }
        if (department.getSortOrder() == null) {
            department.setSortOrder(getMaxSortOrder(department.getParentId()) + 1);
        }
        if (department.getDeptLevel() == null) {
            department.setDeptLevel(calculateDeptLevel(department.getParentId()));
        }
        
        department.setCreateTime(LocalDateTime.now());
        department.setUpdateTime(LocalDateTime.now());

        boolean result = save(department);
        if (result) {
            // 更新部门路径
            updateDepartmentPath(department.getDeptId());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean updateDepartment(Department department) {
        if (department == null || department.getDeptId() == null) {
            throw new BusinessException("部门信息不能为空");
        }

        Department existingDept = getById(department.getDeptId());
        if (existingDept == null) {
            throw new BusinessException("部门不存在");
        }

        // 验证部门编码唯一性（排除自己）
        if (StringUtils.hasText(department.getDeptCode()) && 
            !department.getDeptCode().equals(existingDept.getDeptCode()) &&
            isDeptCodeExists(department.getDeptCode())) {
            throw new BusinessException("部门编码已存在");
        }

        // 验证部门名称在同一父部门下的唯一性（排除自己）
        if (StringUtils.hasText(department.getDeptName()) && 
            (!department.getDeptName().equals(existingDept.getDeptName()) ||
             !Objects.equals(department.getParentId(), existingDept.getParentId())) &&
            isDeptNameExists(department.getDeptName(), department.getParentId())) {
            throw new BusinessException("同一父部门下部门名称已存在");
        }

        // 检查是否移动到自己的子部门下
        if (!Objects.equals(department.getParentId(), existingDept.getParentId())) {
            List<Long> childIds = getAllChildDepartmentIds(department.getDeptId());
            if (childIds.contains(department.getParentId())) {
                throw new BusinessException("不能将部门移动到自己的子部门下");
            }
        }

        department.setUpdateTime(LocalDateTime.now());
        boolean result = updateById(department);
        
        if (result) {
            // 如果父部门发生变化，需要更新路径和层级
            if (!Objects.equals(department.getParentId(), existingDept.getParentId())) {
                updateDepartmentPath(department.getDeptId());
                batchUpdateChildDepartmentPaths(department.getDeptId());
            }
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean deleteDepartment(Long deptId) {
        if (deptId == null) {
            throw new BusinessException("部门ID不能为空");
        }

        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException("部门不存在");
        }

        // 检查是否有子部门
        if (hasChildDepartments(deptId)) {
            throw new BusinessException("存在子部门，无法删除");
        }

        // 检查是否有用户
        if (hasUsers(deptId)) {
            throw new BusinessException("部门下存在用户，无法删除");
        }

        return removeById(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean deleteBatchDepartments(List<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            throw new BusinessException("部门ID列表不能为空");
        }

        for (Long deptId : deptIds) {
            if (hasChildDepartments(deptId)) {
                Department dept = getById(deptId);
                throw new BusinessException("部门【" + (dept != null ? dept.getDeptName() : deptId) + "】存在子部门，无法删除");
            }
            if (hasUsers(deptId)) {
                Department dept = getById(deptId);
                throw new BusinessException("部门【" + (dept != null ? dept.getDeptName() : deptId) + "】下存在用户，无法删除");
            }
        }

        return removeByIds(deptIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean enableDepartment(Long deptId) {
        if (deptId == null) {
            throw new BusinessException("部门ID不能为空");
        }
        return baseMapper.updateDepartmentStatus(deptId, 1) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean disableDepartment(Long deptId) {
        if (deptId == null) {
            throw new BusinessException("部门ID不能为空");
        }
        return baseMapper.updateDepartmentStatus(deptId, 0) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean batchUpdateStatus(List<Long> deptIds, Integer status) {
        if (deptIds == null || deptIds.isEmpty()) {
            throw new BusinessException("部门ID列表不能为空");
        }
        if (status == null) {
            throw new BusinessException("状态不能为空");
        }
        return baseMapper.batchUpdateDepartmentStatus(deptIds, status) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean moveDepartment(Long deptId, Long newParentId) {
        if (deptId == null) {
            throw new BusinessException("部门ID不能为空");
        }

        Department department = getById(deptId);
        if (department == null) {
            throw new BusinessException("部门不存在");
        }

        // 检查是否移动到自己的子部门下
        if (newParentId != null) {
            List<Long> childIds = getAllChildDepartmentIds(deptId);
            if (childIds.contains(newParentId)) {
                throw new BusinessException("不能将部门移动到自己的子部门下");
            }
        }

        department.setParentId(newParentId);
        department.setDeptLevel(calculateDeptLevel(newParentId));
        department.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(department);
        if (result) {
            // 更新路径和子部门路径
            updateDepartmentPath(deptId);
            batchUpdateChildDepartmentPaths(deptId);
        }
        
        return result;
    }

    @Override
    public boolean isDeptCodeExists(String deptCode) {
        if (!StringUtils.hasText(deptCode)) {
            return false;
        }
        return baseMapper.checkDeptCodeExists(deptCode) > 0;
    }

    @Override
    public boolean isDeptNameExists(String deptName, Long parentId) {
        if (!StringUtils.hasText(deptName)) {
            return false;
        }
        return baseMapper.checkDeptNameExists(deptName, parentId) > 0;
    }

    @Override
    @Cacheable(value = "department", key = "'count'")
    public Long countDepartments() {
        return baseMapper.countDepartments();
    }

    @Override
    @Cacheable(value = "department", key = "'countByType:' + #deptType")
    public Long countDepartmentsByType(String deptType) {
        if (!StringUtils.hasText(deptType)) {
            return 0L;
        }
        return baseMapper.countDepartmentsByType(deptType);
    }

    @Override
    @Cacheable(value = "department", key = "'countByStatus:' + #status")
    public Long countDepartmentsByStatus(Integer status) {
        if (status == null) {
            return 0L;
        }
        return baseMapper.countDepartmentsByStatus(status);
    }

    @Override
    public Integer getMaxSortOrder(Long parentId) {
        Integer maxSortOrder = baseMapper.selectMaxSortOrder(parentId);
        return maxSortOrder != null ? maxSortOrder : 0;
    }

    @Override
    @Cacheable(value = "department", key = "'withParent:' + #deptId")
    public Department getDepartmentWithParent(Long deptId) {
        if (deptId == null) {
            return null;
        }
        return baseMapper.selectDepartmentWithParent(deptId);
    }

    @Override
    public boolean hasChildDepartments(Long deptId) {
        if (deptId == null) {
            return false;
        }
        return baseMapper.checkHasChildDepartments(deptId) > 0;
    }

    @Override
    public boolean hasUsers(Long deptId) {
        if (deptId == null) {
            return false;
        }
        return baseMapper.checkHasUsers(deptId) > 0;
    }

    @Override
    @Cacheable(value = "department", key = "'userCount:' + #deptId")
    public Long getDepartmentUserCount(Long deptId) {
        if (deptId == null) {
            return 0L;
        }
        return baseMapper.selectDepartmentUserCount(deptId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean updateDepartmentPath(Long deptId) {
        if (deptId == null) {
            return false;
        }
        
        String path = buildDepartmentPath(deptId);
        return baseMapper.updateDepartmentPath(deptId, path) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean batchUpdateChildDepartmentPaths(Long parentId) {
        if (parentId == null) {
            return false;
        }
        return baseMapper.batchUpdateChildDepartmentPaths(parentId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean updateDepartmentLevel(Long deptId) {
        if (deptId == null) {
            return false;
        }
        
        Department department = getById(deptId);
        if (department == null) {
            return false;
        }
        
        Integer level = calculateDeptLevel(department.getParentId());
        return baseMapper.updateDepartmentLevel(deptId, level) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean batchUpdateChildDepartmentLevels(Long parentId, Integer levelIncrement) {
        if (parentId == null || levelIncrement == null) {
            return false;
        }
        return baseMapper.batchUpdateChildDepartmentLevels(parentId, levelIncrement) > 0;
    }

    @Override
    @Cacheable(value = "department", key = "'pathPrefix:' + #pathPrefix")
    public List<Department> getDepartmentsByPathPrefix(String pathPrefix) {
        if (!StringUtils.hasText(pathPrefix)) {
            return new ArrayList<>();
        }
        return baseMapper.selectDepartmentsByPathPrefix(pathPrefix);
    }

    @Override
    @Cacheable(value = "department", key = "'root'")
    public List<Department> getRootDepartments() {
        return baseMapper.selectRootDepartments();
    }

    @Override
    @Cacheable(value = "department", key = "'leaf'")
    public List<Department> getLeafDepartments() {
        return baseMapper.selectLeafDepartments();
    }

    @Override
    @Cacheable(value = "department", key = "'level:' + #deptLevel")
    public List<Department> getDepartmentsByLevel(Integer deptLevel) {
        if (deptLevel == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectDepartmentsByLevel(deptLevel);
    }

    @Override
    @Cacheable(value = "department", key = "'hierarchy:' + #maxLevel")
    public List<Department> getDepartmentHierarchy(Integer maxLevel) {
        return baseMapper.selectDepartmentHierarchy(maxLevel);
    }

    @Override
    public List<Department> searchDepartments(String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return new ArrayList<>();
        }
        return baseMapper.searchDepartments(keyword);
    }

    @Override
    @Cacheable(value = "department", key = "'accessible:' + #userId")
    public List<Department> getAccessibleDepartments(Long userId) {
        if (userId == null) {
            return new ArrayList<>();
        }
        return baseMapper.selectAccessibleDepartments(userId);
    }

    @Override
    @Cacheable(value = "department", key = "'statistics'")
    public Map<String, Object> getDepartmentStatistics() {
        return baseMapper.selectDepartmentStatistics();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public Department copyDepartment(Long sourceDeptId, String newDeptCode, String newDeptName, Long newParentId) {
        if (sourceDeptId == null) {
            throw new BusinessException("源部门ID不能为空");
        }
        if (!StringUtils.hasText(newDeptCode)) {
            throw new BusinessException("新部门编码不能为空");
        }
        if (!StringUtils.hasText(newDeptName)) {
            throw new BusinessException("新部门名称不能为空");
        }

        Department sourceDept = getById(sourceDeptId);
        if (sourceDept == null) {
            throw new BusinessException("源部门不存在");
        }

        // 验证新部门编码唯一性
        if (isDeptCodeExists(newDeptCode)) {
            throw new BusinessException("新部门编码已存在");
        }

        // 验证新部门名称在同一父部门下的唯一性
        if (isDeptNameExists(newDeptName, newParentId)) {
            throw new BusinessException("同一父部门下新部门名称已存在");
        }

        Department newDept = new Department();
        BeanUtils.copyProperties(sourceDept, newDept);
        newDept.setDeptId(null);
        newDept.setDeptCode(newDeptCode);
        newDept.setDeptName(newDeptName);
        newDept.setParentId(newParentId);
        newDept.setDeptLevel(calculateDeptLevel(newParentId));
        newDept.setSortOrder(getMaxSortOrder(newParentId) + 1);
        newDept.setCreateTime(LocalDateTime.now());
        newDept.setUpdateTime(LocalDateTime.now());

        if (save(newDept)) {
            updateDepartmentPath(newDept.getDeptId());
            return newDept;
        }
        
        throw new BusinessException("复制部门失败");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public ImportResult importDepartments(List<Department> departments) {
        ImportResult result = new ImportResult();
        
        if (departments == null || departments.isEmpty()) {
            result.addFailure("导入数据为空");
            return result;
        }

        for (Department department : departments) {
            try {
                // 验证必填字段
                if (!StringUtils.hasText(department.getDeptCode())) {
                    result.addFailure("部门编码不能为空");
                    continue;
                }
                if (!StringUtils.hasText(department.getDeptName())) {
                    result.addFailure("部门名称不能为空");
                    continue;
                }

                // 检查编码是否已存在
                if (isDeptCodeExists(department.getDeptCode())) {
                    result.addFailure("部门编码【" + department.getDeptCode() + "】已存在");
                    continue;
                }

                // 设置默认值
                if (department.getStatus() == null) {
                    department.setStatus(1);
                }
                if (department.getDeptLevel() == null) {
                    department.setDeptLevel(calculateDeptLevel(department.getParentId()));
                }
                if (department.getSortOrder() == null) {
                    department.setSortOrder(getMaxSortOrder(department.getParentId()) + 1);
                }
                
                department.setCreateTime(LocalDateTime.now());
                department.setUpdateTime(LocalDateTime.now());

                if (save(department)) {
                    updateDepartmentPath(department.getDeptId());
                    result.addSuccess();
                } else {
                    result.addFailure("保存部门【" + department.getDeptName() + "】失败");
                }
            } catch (Exception e) {
                result.addFailure("导入部门【" + department.getDeptName() + "】异常：" + e.getMessage());
            }
        }

        return result;
    }

    @Override
    public List<Department> exportDepartments(List<Long> deptIds) {
        if (deptIds == null || deptIds.isEmpty()) {
            return listByIds(Collections.emptyList());
        }
        return listByIds(deptIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "department", allEntries = true)
    public boolean syncDepartmentHierarchy() {
        try {
            // 获取所有部门
            List<Department> allDepartments = list();
            
            for (Department dept : allDepartments) {
                // 更新层级
                dept.setDeptLevel(calculateDeptLevel(dept.getParentId()));
                updateById(dept);
                
                // 更新路径
                updateDepartmentPath(dept.getDeptId());
            }
            
            return true;
        } catch (Exception e) {
            throw new BusinessException("同步部门层级和路径失败：" + e.getMessage());
        }
    }

    @Override
    @CacheEvict(value = "department", allEntries = true)
    public void clearCache() {
        // 缓存已通过注解清空
    }

    @Override
    public void syncCache() {
        // 预热常用缓存
        getDepartmentTree();
        getEnabledDepartments();
        getRootDepartments();
        countDepartments();
    }

    /**
     * 构建部门树结构
     */
    private List<Department> buildDepartmentTree(List<Department> departments, Long parentId) {
        List<Department> tree = new ArrayList<>();
        
        for (Department dept : departments) {
            if (Objects.equals(dept.getParentId(), parentId)) {
                List<Department> children = buildDepartmentTree(departments, dept.getDeptId());
                dept.setChildren(children);
                tree.add(dept);
            }
        }
        
        // 按排序号排序
        tree.sort(Comparator.comparing(Department::getSortOrder, Comparator.nullsLast(Comparator.naturalOrder())));
        
        return tree;
    }

    /**
     * 计算部门层级
     */
    private Integer calculateDeptLevel(Long parentId) {
        if (parentId == null) {
            return 1;
        }
        
        Department parent = getById(parentId);
        if (parent == null) {
            return 1;
        }
        
        return parent.getDeptLevel() + 1;
    }

    /**
     * 构建部门路径
     */
    private String buildDepartmentPath(Long deptId) {
        if (deptId == null) {
            return "";
        }
        
        Department dept = getById(deptId);
        if (dept == null) {
            return "";
        }
        
        if (dept.getParentId() == null) {
            return "/" + deptId;
        }
        
        String parentPath = buildDepartmentPath(dept.getParentId());
        return parentPath + "/" + deptId;
    }
}