package com.kingdee.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kingdee.integration.entity.Permission;

import java.util.List;

/**
 * 权限服务接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface PermissionService extends IService<Permission> {

    /**
     * 分页查询权限列表
     *
     * @param page 分页参数
     * @param permissionCode 权限编码
     * @param permissionName 权限名称
     * @param permissionType 权限类型
     * @param parentId 父权限ID
     * @param status 状态
     * @return 分页结果
     */
    IPage<Permission> pagePermissions(Page<Permission> page, String permissionCode, String permissionName, 
                                    String permissionType, Long parentId, Integer status);

    /**
     * 获取权限树结构
     *
     * @param permissionType 权限类型
     * @param enabledOnly 是否只显示启用的
     * @return 权限树
     */
    List<Permission> getPermissionTree(String permissionType, Boolean enabledOnly);

    /**
     * 根据权限编码查询权限
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Permission getPermissionByCode(String permissionCode);

    /**
     * 根据权限名称查询权限
     *
     * @param permissionName 权限名称
     * @return 权限信息
     */
    Permission getPermissionByName(String permissionName);

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 创建的权限
     */
    Permission createPermission(Permission permission);

    /**
     * 更新权限
     *
     * @param permission 权限信息
     * @return 更新的权限
     */
    Permission updatePermission(Permission permission);

    /**
     * 删除权限
     *
     * @param id 权限ID
     * @return 是否删除成功
     */
    boolean deletePermission(Long id);

    /**
     * 批量删除权限
     *
     * @param permissionIds 权限ID列表
     * @return 是否删除成功
     */
    boolean deletePermissions(List<Long> permissionIds);

    /**
     * 启用权限
     *
     * @param id 权限ID
     * @return 是否启用成功
     */
    boolean enablePermission(Long id);

    /**
     * 禁用权限
     *
     * @param id 权限ID
     * @return 是否禁用成功
     */
    boolean disablePermission(Long id);

    /**
     * 批量更新权限状态
     *
     * @param permissionIds 权限ID列表
     * @param status 状态
     * @return 是否更新成功
     */
    boolean batchUpdateStatus(List<Long> permissionIds, Integer status);

    /**
     * 获取所有启用的权限
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<Permission> getEnabledPermissions(String permissionType);

    /**
     * 根据权限类型查询权限列表
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<Permission> getPermissionsByType(String permissionType);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleId(Long roleId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByUserId(Long userId);

    /**
     * 获取菜单权限列表
     *
     * @param enabledOnly 是否只显示启用的
     * @return 菜单权限列表
     */
    List<Permission> getMenuPermissions(Boolean enabledOnly);

    /**
     * 获取按钮权限列表
     *
     * @param parentId 父权限ID
     * @param enabledOnly 是否只显示启用的
     * @return 按钮权限列表
     */
    List<Permission> getButtonPermissions(Long parentId, Boolean enabledOnly);

    /**
     * 获取API权限列表
     *
     * @param enabledOnly 是否只显示启用的
     * @return API权限列表
     */
    List<Permission> getApiPermissions(Boolean enabledOnly);

    /**
     * 获取子权限列表
     *
     * @param parentId 父权限ID
     * @param enabledOnly 是否只显示启用的
     * @return 子权限列表
     */
    List<Permission> getChildPermissions(Long parentId, Boolean enabledOnly);

    /**
     * 获取权限路径
     *
     * @param id 权限ID
     * @return 权限路径
     */
    String getPermissionPath(Long id);

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @param excludePermissionId 排除的权限ID
     * @return 是否存在
     */
    boolean existsByPermissionCode(String permissionCode, Long excludePermissionId);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param parentId 父权限ID
     * @param excludePermissionId 排除的权限ID
     * @return 是否存在
     */
    boolean existsByPermissionName(String permissionName, Long parentId, Long excludePermissionId);

    /**
     * 检查权限URL是否存在
     *
     * @param permissionUrl 权限URL
     * @param httpMethod HTTP方法
     * @param excludePermissionId 排除的权限ID
     * @return 是否存在
     */
    boolean existsByPermissionUrl(String permissionUrl, String httpMethod, Long excludePermissionId);

    /**
     * 统计权限数量
     *
     * @param permissionType 权限类型
     * @param status 状态
     * @param parentId 父权限ID
     * @return 权限数量
     */
    Long countPermissions(String permissionType, Integer status, Long parentId);

    /**
     * 获取最大排序号
     *
     * @param parentId 父权限ID
     * @return 最大排序号
     */
    Integer getMaxSortOrder(Long parentId);

    /**
     * 获取权限详情（包含父权限信息）
     *
     * @param id 权限ID
     * @return 权限详情
     */
    Permission getPermissionDetail(Long id);

    /**
     * 检查权限是否被使用
     *
     * @param id 权限ID
     * @return 是否被使用
     */
    boolean isPermissionInUse(Long id);

    /**
     * 根据URL和方法查询权限
     *
     * @param permissionUrl 权限URL
     * @param httpMethod HTTP方法
     * @return 权限信息
     */
    Permission getPermissionByUrl(String permissionUrl, String httpMethod);

    /**
     * 获取用户菜单树
     *
     * @param userId 用户ID
     * @return 菜单权限树
     */
    List<Permission> getUserMenuTree(Long userId);

    /**
     * 获取用户按钮权限
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<String> getUserButtonPermissions(Long userId);

    /**
     * 获取用户API权限
     *
     * @param userId 用户ID
     * @return API权限列表
     */
    List<String> getUserApiPermissions(Long userId);

    /**
     * 根据角色ID列表查询权限
     *
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleIds(List<Long> roleIds);

    /**
     * 获取系统权限列表
     *
     * @return 系统权限列表
     */
    List<Permission> getSystemPermissions();

    /**
     * 获取业务权限列表
     *
     * @return 业务权限列表
     */
    List<Permission> getBusinessPermissions();

    /**
     * 更新权限路径
     *
     * @param id 权限ID
     * @return 是否更新成功
     */
    boolean updatePermissionPath(Long id);

    /**
     * 批量更新子权限路径
     *
     * @param parentId 父权限ID
     * @param newParentPath 新的父路径
     * @return 是否更新成功
     */
    boolean batchUpdateChildPaths(Long parentId, String newParentPath);
}