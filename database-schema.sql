-- 金蝶云星辰业务扩展与集成平台 - 数据库Schema设计
-- PostgreSQL 14+ 兼容

-- 创建数据库（如果需要）
-- CREATE DATABASE kingdee_integration_platform;

-- 使用数据库
-- \c kingdee_integration_platform;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- =============================================================================
-- 系统管理相关表
-- =============================================================================

-- 用户表
CREATE TABLE sys_users (
    id BIGSERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    real_name VARCHAR(100),
    avatar_url VARCHAR(500),
    status INTEGER DEFAULT 1, -- 1:正常 0:禁用 -1:删除
    last_login_at TIMESTAMP,
    last_login_ip INET,
    login_count INTEGER DEFAULT 0,
    mfa_enabled BOOLEAN DEFAULT FALSE,
    mfa_secret VARCHAR(100),
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    language VARCHAR(10) DEFAULT 'zh-CN',
    timezone VARCHAR(50) DEFAULT 'Asia/Shanghai',
    theme VARCHAR(20) DEFAULT 'light',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 角色表
CREATE TABLE sys_roles (
    id BIGSERIAL PRIMARY KEY,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    role_name VARCHAR(100) NOT NULL,
    description TEXT,
    status INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 权限表
CREATE TABLE sys_permissions (
    id BIGSERIAL PRIMARY KEY,
    permission_code VARCHAR(100) UNIQUE NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    resource_type VARCHAR(20) NOT NULL, -- menu, button, api
    resource_path VARCHAR(200),
    parent_id BIGINT,
    description TEXT,
    status INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 用户角色关联表
CREATE TABLE sys_user_roles (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE sys_role_permissions (
    id BIGSERIAL PRIMARY KEY,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    UNIQUE(role_id, permission_id)
);

-- 用户登录历史表
CREATE TABLE sys_user_login_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    login_type VARCHAR(20) NOT NULL, -- password, oauth, sms
    login_ip INET,
    user_agent TEXT,
    device_info JSONB,
    location_info JSONB,
    login_result VARCHAR(20) NOT NULL, -- success, failed
    failure_reason VARCHAR(100),
    login_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 系统配置表
CREATE TABLE sys_configs (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(20) DEFAULT 'string', -- string, number, boolean, json
    description TEXT,
    is_encrypted BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT
);

-- =============================================================================
-- 组织架构相关表
-- =============================================================================

-- 组织架构表
CREATE TABLE biz_organizations (
    id BIGSERIAL PRIMARY KEY,
    org_code VARCHAR(50) UNIQUE NOT NULL,
    org_name VARCHAR(100) NOT NULL,
    org_type VARCHAR(20) NOT NULL, -- company, department, team
    parent_id BIGINT,
    level_path VARCHAR(500), -- 层级路径，如：1/2/3
    manager_id BIGINT,
    description TEXT,
    status INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    external_id VARCHAR(100), -- 外部系统ID
    sync_source VARCHAR(50), -- 同步来源：kingdee, dingtalk, feishu
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 用户组织关联表
CREATE TABLE biz_user_organizations (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT NOT NULL,
    org_id BIGINT NOT NULL,
    position VARCHAR(100),
    is_primary BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    UNIQUE(user_id, org_id)
);

-- =============================================================================
-- 主数据相关表
-- =============================================================================

-- 物料主数据表
CREATE TABLE biz_materials (
    id BIGSERIAL PRIMARY KEY,
    material_code VARCHAR(50) UNIQUE NOT NULL,
    material_name VARCHAR(200) NOT NULL,
    material_spec VARCHAR(200),
    material_model VARCHAR(100),
    category_id BIGINT,
    unit_id BIGINT,
    brand VARCHAR(100),
    manufacturer VARCHAR(200),
    purchase_price DECIMAL(15,4),
    sale_price DECIMAL(15,4),
    cost_price DECIMAL(15,4),
    min_stock DECIMAL(15,4),
    max_stock DECIMAL(15,4),
    safety_stock DECIMAL(15,4),
    lead_time INTEGER, -- 采购提前期（天）
    shelf_life INTEGER, -- 保质期（天）
    status INTEGER DEFAULT 1,
    remark TEXT,
    external_id VARCHAR(100), -- 金蝶系统ID
    sync_status VARCHAR(20) DEFAULT 'pending', -- pending, synced, failed
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 物料分类表
CREATE TABLE biz_material_categories (
    id BIGSERIAL PRIMARY KEY,
    category_code VARCHAR(50) UNIQUE NOT NULL,
    category_name VARCHAR(100) NOT NULL,
    parent_id BIGINT,
    level_path VARCHAR(500),
    description TEXT,
    status INTEGER DEFAULT 1,
    sort_order INTEGER DEFAULT 0,
    external_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 计量单位表
CREATE TABLE biz_units (
    id BIGSERIAL PRIMARY KEY,
    unit_code VARCHAR(20) UNIQUE NOT NULL,
    unit_name VARCHAR(50) NOT NULL,
    unit_type VARCHAR(20), -- length, weight, volume, quantity
    base_unit_id BIGINT, -- 基本单位ID
    conversion_rate DECIMAL(15,6) DEFAULT 1, -- 换算率
    precision_digits INTEGER DEFAULT 2,
    status INTEGER DEFAULT 1,
    external_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 客户主数据表
CREATE TABLE biz_customers (
    id BIGSERIAL PRIMARY KEY,
    customer_code VARCHAR(50) UNIQUE NOT NULL,
    customer_name VARCHAR(200) NOT NULL,
    customer_type VARCHAR(20) DEFAULT 'enterprise', -- enterprise, individual
    industry VARCHAR(100),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(100),
    address TEXT,
    postal_code VARCHAR(20),
    tax_number VARCHAR(50),
    credit_limit DECIMAL(15,2),
    payment_terms VARCHAR(100),
    sales_rep_id BIGINT,
    status INTEGER DEFAULT 1,
    remark TEXT,
    external_id VARCHAR(100),
    sync_status VARCHAR(20) DEFAULT 'pending',
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 供应商主数据表
CREATE TABLE biz_suppliers (
    id BIGSERIAL PRIMARY KEY,
    supplier_code VARCHAR(50) UNIQUE NOT NULL,
    supplier_name VARCHAR(200) NOT NULL,
    supplier_type VARCHAR(20) DEFAULT 'enterprise',
    industry VARCHAR(100),
    contact_person VARCHAR(100),
    contact_phone VARCHAR(50),
    contact_email VARCHAR(100),
    address TEXT,
    postal_code VARCHAR(20),
    tax_number VARCHAR(50),
    payment_terms VARCHAR(100),
    purchase_rep_id BIGINT,
    qualification_level VARCHAR(20), -- A, B, C, D
    status INTEGER DEFAULT 1,
    remark TEXT,
    external_id VARCHAR(100),
    sync_status VARCHAR(20) DEFAULT 'pending',
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- =============================================================================
-- 业务单据相关表
-- =============================================================================

-- 单据类型表
CREATE TABLE biz_document_types (
    id BIGSERIAL PRIMARY KEY,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    type_name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL, -- purchase, sale, inventory, finance
    number_rule VARCHAR(200), -- 编号规则
    workflow_id BIGINT, -- 关联工作流
    is_system BOOLEAN DEFAULT FALSE,
    status INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 业务单据主表
CREATE TABLE biz_documents (
    id BIGSERIAL PRIMARY KEY,
    document_number VARCHAR(100) UNIQUE NOT NULL,
    document_type_id BIGINT NOT NULL,
    document_date DATE NOT NULL,
    business_partner_id BIGINT, -- 业务伙伴ID（客户或供应商）
    business_partner_type VARCHAR(20), -- customer, supplier
    total_amount DECIMAL(15,2) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    net_amount DECIMAL(15,2) DEFAULT 0,
    currency_code VARCHAR(10) DEFAULT 'CNY',
    exchange_rate DECIMAL(10,6) DEFAULT 1,
    payment_terms VARCHAR(100),
    delivery_date DATE,
    delivery_address TEXT,
    remark TEXT,
    status VARCHAR(20) DEFAULT 'draft', -- draft, submitted, approved, rejected, closed
    workflow_instance_id BIGINT,
    external_id VARCHAR(100),
    sync_status VARCHAR(20) DEFAULT 'pending',
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 业务单据明细表
CREATE TABLE biz_document_items (
    id BIGSERIAL PRIMARY KEY,
    document_id BIGINT NOT NULL,
    line_number INTEGER NOT NULL,
    material_id BIGINT NOT NULL,
    quantity DECIMAL(15,4) NOT NULL,
    unit_id BIGINT NOT NULL,
    unit_price DECIMAL(15,4) NOT NULL,
    total_price DECIMAL(15,2) NOT NULL,
    tax_rate DECIMAL(5,4) DEFAULT 0,
    tax_amount DECIMAL(15,2) DEFAULT 0,
    discount_rate DECIMAL(5,4) DEFAULT 0,
    discount_amount DECIMAL(15,2) DEFAULT 0,
    net_amount DECIMAL(15,2) NOT NULL,
    delivery_date DATE,
    remark TEXT,
    external_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    UNIQUE(document_id, line_number)
);

-- =============================================================================
-- 工作流相关表
-- =============================================================================

-- 工作流定义表
CREATE TABLE biz_workflows (
    id BIGSERIAL PRIMARY KEY,
    workflow_code VARCHAR(50) UNIQUE NOT NULL,
    workflow_name VARCHAR(100) NOT NULL,
    workflow_type VARCHAR(50) NOT NULL,
    definition_json JSONB NOT NULL, -- 工作流定义JSON
    is_active BOOLEAN DEFAULT TRUE,
    version_number INTEGER DEFAULT 1,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE
);

-- 工作流实例表
CREATE TABLE biz_workflow_instances (
    id BIGSERIAL PRIMARY KEY,
    workflow_id BIGINT NOT NULL,
    instance_key VARCHAR(100) UNIQUE NOT NULL,
    business_key VARCHAR(100),
    business_type VARCHAR(50),
    business_id BIGINT,
    current_node VARCHAR(100),
    status VARCHAR(20) DEFAULT 'running', -- running, completed, terminated, suspended
    variables JSONB,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    started_by BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 工作流任务表
CREATE TABLE biz_workflow_tasks (
    id BIGSERIAL PRIMARY KEY,
    instance_id BIGINT NOT NULL,
    task_key VARCHAR(100) NOT NULL,
    task_name VARCHAR(100) NOT NULL,
    task_type VARCHAR(50) NOT NULL, -- user_task, service_task, script_task
    assignee_id BIGINT,
    assignee_type VARCHAR(20), -- user, role, group
    status VARCHAR(20) DEFAULT 'created', -- created, assigned, completed, cancelled
    priority INTEGER DEFAULT 50,
    due_date TIMESTAMP,
    variables JSONB,
    form_data JSONB,
    comment TEXT,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================================
-- 集成相关表
-- =============================================================================

-- 系统集成配置表
CREATE TABLE sys_integrations (
    id BIGSERIAL PRIMARY KEY,
    integration_code VARCHAR(50) UNIQUE NOT NULL,
    integration_name VARCHAR(100) NOT NULL,
    integration_type VARCHAR(50) NOT NULL, -- kingdee, dingtalk, feishu, wechat
    endpoint_url VARCHAR(500),
    app_id VARCHAR(100),
    app_secret VARCHAR(500), -- 加密存储
    access_token VARCHAR(1000),
    refresh_token VARCHAR(1000),
    token_expires_at TIMESTAMP,
    config_json JSONB, -- 其他配置参数
    is_active BOOLEAN DEFAULT TRUE,
    last_sync_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 数据同步日志表
CREATE TABLE sys_sync_logs (
    id BIGSERIAL PRIMARY KEY,
    integration_id BIGINT NOT NULL,
    sync_type VARCHAR(50) NOT NULL, -- full, incremental
    sync_direction VARCHAR(20) NOT NULL, -- import, export, bidirectional
    data_type VARCHAR(50) NOT NULL, -- material, customer, supplier, document
    sync_status VARCHAR(20) NOT NULL, -- running, success, failed, partial
    total_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failed_count INTEGER DEFAULT 0,
    error_message TEXT,
    sync_details JSONB,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    created_by BIGINT
);

-- API调用日志表
CREATE TABLE sys_api_logs (
    id BIGSERIAL PRIMARY KEY,
    request_id VARCHAR(100) UNIQUE NOT NULL,
    integration_id BIGINT,
    api_path VARCHAR(500) NOT NULL,
    http_method VARCHAR(10) NOT NULL,
    request_headers JSONB,
    request_body TEXT,
    response_status INTEGER,
    response_headers JSONB,
    response_body TEXT,
    response_time INTEGER, -- 响应时间（毫秒）
    client_ip INET,
    user_agent TEXT,
    user_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================================
-- 通知相关表
-- =============================================================================

-- 通知模板表
CREATE TABLE sys_notification_templates (
    id BIGSERIAL PRIMARY KEY,
    template_code VARCHAR(50) UNIQUE NOT NULL,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL, -- email, sms, push, webhook
    subject_template TEXT,
    content_template TEXT NOT NULL,
    variables JSONB, -- 模板变量定义
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- 通知记录表
CREATE TABLE sys_notifications (
    id BIGSERIAL PRIMARY KEY,
    notification_type VARCHAR(20) NOT NULL,
    recipient_type VARCHAR(20) NOT NULL, -- user, role, group, external
    recipient_id VARCHAR(100) NOT NULL,
    recipient_address VARCHAR(200), -- 邮箱、手机号、webhook地址等
    subject VARCHAR(500),
    content TEXT NOT NULL,
    send_status VARCHAR(20) DEFAULT 'pending', -- pending, sent, failed, cancelled
    send_time TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    business_type VARCHAR(50),
    business_id BIGINT,
    template_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT
);

-- =============================================================================
-- 文件管理相关表
-- =============================================================================

-- 文件信息表
CREATE TABLE sys_files (
    id BIGSERIAL PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(1000) NOT NULL,
    file_size BIGINT NOT NULL,
    file_type VARCHAR(100),
    mime_type VARCHAR(100),
    file_hash VARCHAR(64), -- SHA256哈希
    storage_type VARCHAR(20) DEFAULT 'local', -- local, minio, oss, cos
    bucket_name VARCHAR(100),
    is_public BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    business_type VARCHAR(50),
    business_id BIGINT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by BIGINT,
    updated_by BIGINT,
    deleted BOOLEAN DEFAULT FALSE,
    version INTEGER DEFAULT 1
);

-- =============================================================================
-- 审计日志表
-- =============================================================================

-- 操作审计日志表
CREATE TABLE sys_audit_logs (
    id BIGSERIAL PRIMARY KEY,
    user_id BIGINT,
    username VARCHAR(50),
    operation_type VARCHAR(50) NOT NULL, -- CREATE, UPDATE, DELETE, LOGIN, LOGOUT
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(100),
    operation_desc TEXT,
    old_values JSONB,
    new_values JSONB,
    client_ip INET,
    user_agent TEXT,
    request_id VARCHAR(100),
    operation_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- =============================================================================
-- 索引创建
-- =============================================================================

-- 用户表索引
CREATE INDEX idx_users_username ON sys_users(username) WHERE deleted = FALSE;
CREATE INDEX idx_users_email ON sys_users(email) WHERE deleted = FALSE;
CREATE INDEX idx_users_phone ON sys_users(phone) WHERE deleted = FALSE;
CREATE INDEX idx_users_status ON sys_users(status) WHERE deleted = FALSE;

-- 角色权限索引
CREATE INDEX idx_user_roles_user_id ON sys_user_roles(user_id);
CREATE INDEX idx_user_roles_role_id ON sys_user_roles(role_id);
CREATE INDEX idx_role_permissions_role_id ON sys_role_permissions(role_id);
CREATE INDEX idx_role_permissions_permission_id ON sys_role_permissions(permission_id);

-- 组织架构索引
CREATE INDEX idx_organizations_parent_id ON biz_organizations(parent_id);
CREATE INDEX idx_organizations_code ON biz_organizations(org_code) WHERE deleted = FALSE;
CREATE INDEX idx_user_organizations_user_id ON biz_user_organizations(user_id);
CREATE INDEX idx_user_organizations_org_id ON biz_user_organizations(org_id);

-- 主数据索引
CREATE INDEX idx_materials_code ON biz_materials(material_code) WHERE deleted = FALSE;
CREATE INDEX idx_materials_name ON biz_materials USING gin(material_name gin_trgm_ops) WHERE deleted = FALSE;
CREATE INDEX idx_materials_category_id ON biz_materials(category_id) WHERE deleted = FALSE;
CREATE INDEX idx_materials_external_id ON biz_materials(external_id) WHERE deleted = FALSE;
CREATE INDEX idx_materials_sync_status ON biz_materials(sync_status) WHERE deleted = FALSE;

CREATE INDEX idx_customers_code ON biz_customers(customer_code) WHERE deleted = FALSE;
CREATE INDEX idx_customers_name ON biz_customers USING gin(customer_name gin_trgm_ops) WHERE deleted = FALSE;
CREATE INDEX idx_customers_external_id ON biz_customers(external_id) WHERE deleted = FALSE;

CREATE INDEX idx_suppliers_code ON biz_suppliers(supplier_code) WHERE deleted = FALSE;
CREATE INDEX idx_suppliers_name ON biz_suppliers USING gin(supplier_name gin_trgm_ops) WHERE deleted = FALSE;
CREATE INDEX idx_suppliers_external_id ON biz_suppliers(external_id) WHERE deleted = FALSE;

-- 业务单据索引
CREATE INDEX idx_documents_number ON biz_documents(document_number) WHERE deleted = FALSE;
CREATE INDEX idx_documents_type_id ON biz_documents(document_type_id) WHERE deleted = FALSE;
CREATE INDEX idx_documents_date ON biz_documents(document_date) WHERE deleted = FALSE;
CREATE INDEX idx_documents_status ON biz_documents(status) WHERE deleted = FALSE;
CREATE INDEX idx_documents_partner ON biz_documents(business_partner_id, business_partner_type) WHERE deleted = FALSE;
CREATE INDEX idx_documents_external_id ON biz_documents(external_id) WHERE deleted = FALSE;

CREATE INDEX idx_document_items_document_id ON biz_document_items(document_id);
CREATE INDEX idx_document_items_material_id ON biz_document_items(material_id);

-- 工作流索引
CREATE INDEX idx_workflow_instances_workflow_id ON biz_workflow_instances(workflow_id);
CREATE INDEX idx_workflow_instances_business ON biz_workflow_instances(business_type, business_id);
CREATE INDEX idx_workflow_instances_status ON biz_workflow_instances(status);

CREATE INDEX idx_workflow_tasks_instance_id ON biz_workflow_tasks(instance_id);
CREATE INDEX idx_workflow_tasks_assignee ON biz_workflow_tasks(assignee_id, assignee_type);
CREATE INDEX idx_workflow_tasks_status ON biz_workflow_tasks(status);

-- 集成相关索引
CREATE INDEX idx_sync_logs_integration_id ON sys_sync_logs(integration_id);
CREATE INDEX idx_sync_logs_data_type ON sys_sync_logs(data_type);
CREATE INDEX idx_sync_logs_status ON sys_sync_logs(sync_status);
CREATE INDEX idx_sync_logs_start_time ON sys_sync_logs(start_time);

CREATE INDEX idx_api_logs_integration_id ON sys_api_logs(integration_id);
CREATE INDEX idx_api_logs_user_id ON sys_api_logs(user_id);
CREATE INDEX idx_api_logs_created_at ON sys_api_logs(created_at);
CREATE INDEX idx_api_logs_response_time ON sys_api_logs(response_time);

-- 通知索引
CREATE INDEX idx_notifications_recipient ON sys_notifications(recipient_type, recipient_id);
CREATE INDEX idx_notifications_status ON sys_notifications(send_status);
CREATE INDEX idx_notifications_business ON sys_notifications(business_type, business_id);
CREATE INDEX idx_notifications_created_at ON sys_notifications(created_at);

-- 文件索引
CREATE INDEX idx_files_business ON sys_files(business_type, business_id) WHERE deleted = FALSE;
CREATE INDEX idx_files_hash ON sys_files(file_hash) WHERE deleted = FALSE;
CREATE INDEX idx_files_created_by ON sys_files(created_by) WHERE deleted = FALSE;

-- 审计日志索引
CREATE INDEX idx_audit_logs_user_id ON sys_audit_logs(user_id);
CREATE INDEX idx_audit_logs_operation_type ON sys_audit_logs(operation_type);
CREATE INDEX idx_audit_logs_resource ON sys_audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_operation_time ON sys_audit_logs(operation_time);

-- =============================================================================
-- 外键约束
-- =============================================================================

-- 用户角色权限外键
ALTER TABLE sys_user_roles ADD CONSTRAINT fk_user_roles_user_id FOREIGN KEY (user_id) REFERENCES sys_users(id);
ALTER TABLE sys_user_roles ADD CONSTRAINT fk_user_roles_role_id FOREIGN KEY (role_id) REFERENCES sys_roles(id);
ALTER TABLE sys_role_permissions ADD CONSTRAINT fk_role_permissions_role_id FOREIGN KEY (role_id) REFERENCES sys_roles(id);
ALTER TABLE sys_role_permissions ADD CONSTRAINT fk_role_permissions_permission_id FOREIGN KEY (permission_id) REFERENCES sys_permissions(id);

-- 组织架构外键
ALTER TABLE biz_organizations ADD CONSTRAINT fk_organizations_parent_id FOREIGN KEY (parent_id) REFERENCES biz_organizations(id);
ALTER TABLE biz_organizations ADD CONSTRAINT fk_organizations_manager_id FOREIGN KEY (manager_id) REFERENCES sys_users(id);
ALTER TABLE biz_user_organizations ADD CONSTRAINT fk_user_organizations_user_id FOREIGN KEY (user_id) REFERENCES sys_users(id);
ALTER TABLE biz_user_organizations ADD CONSTRAINT fk_user_organizations_org_id FOREIGN KEY (org_id) REFERENCES biz_organizations(id);

-- 主数据外键
ALTER TABLE biz_materials ADD CONSTRAINT fk_materials_category_id FOREIGN KEY (category_id) REFERENCES biz_material_categories(id);
ALTER TABLE biz_materials ADD CONSTRAINT fk_materials_unit_id FOREIGN KEY (unit_id) REFERENCES biz_units(id);
ALTER TABLE biz_material_categories ADD CONSTRAINT fk_material_categories_parent_id FOREIGN KEY (parent_id) REFERENCES biz_material_categories(id);
ALTER TABLE biz_units ADD CONSTRAINT fk_units_base_unit_id FOREIGN KEY (base_unit_id) REFERENCES biz_units(id);

-- 业务单据外键
ALTER TABLE biz_documents ADD CONSTRAINT fk_documents_document_type_id FOREIGN KEY (document_type_id) REFERENCES biz_document_types(id);
ALTER TABLE biz_documents ADD CONSTRAINT fk_documents_workflow_instance_id FOREIGN KEY (workflow_instance_id) REFERENCES biz_workflow_instances(id);
ALTER TABLE biz_document_items ADD CONSTRAINT fk_document_items_document_id FOREIGN KEY (document_id) REFERENCES biz_documents(id);
ALTER TABLE biz_document_items ADD CONSTRAINT fk_document_items_material_id FOREIGN KEY (material_id) REFERENCES biz_materials(id);
ALTER TABLE biz_document_items ADD CONSTRAINT fk_document_items_unit_id FOREIGN KEY (unit_id) REFERENCES biz_units(id);

-- 工作流外键
ALTER TABLE biz_workflow_instances ADD CONSTRAINT fk_workflow_instances_workflow_id FOREIGN KEY (workflow_id) REFERENCES biz_workflows(id);
ALTER TABLE biz_workflow_instances ADD CONSTRAINT fk_workflow_instances_started_by FOREIGN KEY (started_by) REFERENCES sys_users(id);
ALTER TABLE biz_workflow_tasks ADD CONSTRAINT fk_workflow_tasks_instance_id FOREIGN KEY (instance_id) REFERENCES biz_workflow_instances(id);

-- 集成相关外键
ALTER TABLE sys_sync_logs ADD CONSTRAINT fk_sync_logs_integration_id FOREIGN KEY (integration_id) REFERENCES sys_integrations(id);
ALTER TABLE sys_api_logs ADD CONSTRAINT fk_api_logs_integration_id FOREIGN KEY (integration_id) REFERENCES sys_integrations(id);
ALTER TABLE sys_api_logs ADD CONSTRAINT fk_api_logs_user_id FOREIGN KEY (user_id) REFERENCES sys_users(id);

-- 通知外键
ALTER TABLE sys_notifications ADD CONSTRAINT fk_notifications_template_id FOREIGN KEY (template_id) REFERENCES sys_notification_templates(id);

-- =============================================================================
-- 触发器和函数
-- =============================================================================

-- 更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为所有有updated_at字段的表创建触发器
CREATE TRIGGER update_sys_users_updated_at BEFORE UPDATE ON sys_users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_roles_updated_at BEFORE UPDATE ON sys_roles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_permissions_updated_at BEFORE UPDATE ON sys_permissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_configs_updated_at BEFORE UPDATE ON sys_configs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_organizations_updated_at BEFORE UPDATE ON biz_organizations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_materials_updated_at BEFORE UPDATE ON biz_materials FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_material_categories_updated_at BEFORE UPDATE ON biz_material_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_units_updated_at BEFORE UPDATE ON biz_units FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_customers_updated_at BEFORE UPDATE ON biz_customers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_suppliers_updated_at BEFORE UPDATE ON biz_suppliers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_document_types_updated_at BEFORE UPDATE ON biz_document_types FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_documents_updated_at BEFORE UPDATE ON biz_documents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_document_items_updated_at BEFORE UPDATE ON biz_document_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_integrations_updated_at BEFORE UPDATE ON sys_integrations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_notification_templates_updated_at BEFORE UPDATE ON sys_notification_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_notifications_updated_at BEFORE UPDATE ON sys_notifications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_sys_files_updated_at BEFORE UPDATE ON sys_files FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_workflow_instances_updated_at BEFORE UPDATE ON biz_workflow_instances FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_biz_workflow_tasks_updated_at BEFORE UPDATE ON biz_workflow_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =============================================================================
-- 初始数据插入
-- =============================================================================

-- 插入默认角色
INSERT INTO sys_roles (role_code, role_name, description, created_by) VALUES
('SUPER_ADMIN', '超级管理员', '系统超级管理员，拥有所有权限', 1),
('ADMIN', '系统管理员', '系统管理员，拥有大部分管理权限', 1),
('USER', '普通用户', '普通用户，拥有基本业务权限', 1),
('GUEST', '访客', '访客用户，只有查看权限', 1);

-- 插入默认权限
INSERT INTO sys_permissions (permission_code, permission_name, resource_type, resource_path, description, created_by) VALUES
('SYSTEM_MANAGE', '系统管理', 'menu', '/system', '系统管理菜单', 1),
('USER_MANAGE', '用户管理', 'menu', '/system/users', '用户管理菜单', 1),
('ROLE_MANAGE', '角色管理', 'menu', '/system/roles', '角色管理菜单', 1),
('PERMISSION_MANAGE', '权限管理', 'menu', '/system/permissions', '权限管理菜单', 1),
('BUSINESS_MANAGE', '业务管理', 'menu', '/business', '业务管理菜单', 1),
('MATERIAL_MANAGE', '物料管理', 'menu', '/business/materials', '物料管理菜单', 1),
('CUSTOMER_MANAGE', '客户管理', 'menu', '/business/customers', '客户管理菜单', 1),
('SUPPLIER_MANAGE', '供应商管理', 'menu', '/business/suppliers', '供应商管理菜单', 1),
('DOCUMENT_MANAGE', '单据管理', 'menu', '/business/documents', '单据管理菜单', 1),
('WORKFLOW_MANAGE', '工作流管理', 'menu', '/business/workflows', '工作流管理菜单', 1),
('INTEGRATION_MANAGE', '集成管理', 'menu', '/integration', '集成管理菜单', 1),
('SYNC_MANAGE', '同步管理', 'menu', '/integration/sync', '同步管理菜单', 1),
('MONITOR_MANAGE', '监控管理', 'menu', '/monitor', '监控管理菜单', 1);

-- 插入默认系统配置
INSERT INTO sys_configs (config_key, config_value, config_type, description, is_public, created_by) VALUES
('system.name', '金蝶云星辰业务扩展与集成平台', 'string', '系统名称', true, 1),
('system.version', '1.0.0', 'string', '系统版本', true, 1),
('system.logo', '/assets/logo.png', 'string', '系统Logo', true, 1),
('system.copyright', '© 2024 金蝶云星辰业务扩展与集成平台', 'string', '版权信息', true, 1),
('auth.jwt.secret', 'your-jwt-secret-key-here', 'string', 'JWT密钥', false, 1),
('auth.jwt.expiration', '86400', 'number', 'JWT过期时间（秒）', false, 1),
('auth.mfa.enabled', 'false', 'boolean', '是否启用多因素认证', false, 1),
('sync.batch.size', '100', 'number', '同步批次大小', false, 1),
('sync.retry.times', '3', 'number', '同步重试次数', false, 1),
('notification.email.enabled', 'true', 'boolean', '是否启用邮件通知', false, 1),
('notification.sms.enabled', 'false', 'boolean', '是否启用短信通知', false, 1);

-- 插入默认单据类型
INSERT INTO biz_document_types (type_code, type_name, category, number_rule, created_by) VALUES
('PO', '采购订单', 'purchase', 'PO{YYYY}{MM}{DD}{####}', 1),
('SO', '销售订单', 'sale', 'SO{YYYY}{MM}{DD}{####}', 1),
('PR', '采购入库单', 'inventory', 'PR{YYYY}{MM}{DD}{####}', 1),
('SR', '销售出库单', 'inventory', 'SR{YYYY}{MM}{DD}{####}', 1),
('PI', '采购发票', 'finance', 'PI{YYYY}{MM}{DD}{####}', 1),
('SI', '销售发票', 'finance', 'SI{YYYY}{MM}{DD}{####}', 1);

-- 插入默认计量单位
INSERT INTO biz_units (unit_code, unit_name, unit_type, precision_digits, created_by) VALUES
('PCS', '个', 'quantity', 0, 1),
('KG', '千克', 'weight', 3, 1),
('G', '克', 'weight', 3, 1),
('M', '米', 'length', 3, 1),
('CM', '厘米', 'length', 2, 1),
('L', '升', 'volume', 3, 1),
('ML', '毫升', 'volume', 2, 1),
('BOX', '箱', 'quantity', 0, 1),
('SET', '套', 'quantity', 0, 1),
('PAIR', '对', 'quantity', 0, 1);

-- 设置单位换算关系
UPDATE biz_units SET base_unit_id = (SELECT id FROM biz_units WHERE unit_code = 'KG'), conversion_rate = 0.001 WHERE unit_code = 'G';
UPDATE biz_units SET base_unit_id = (SELECT id FROM biz_units WHERE unit_code = 'M'), conversion_rate = 0.01 WHERE unit_code = 'CM';
UPDATE biz_units SET base_unit_id = (SELECT id FROM biz_units WHERE unit_code = 'L'), conversion_rate = 0.001 WHERE unit_code = 'ML';

-- 插入默认物料分类
INSERT INTO biz_material_categories (category_code, category_name, level_path, created_by) VALUES
('RAW', '原材料', '1', 1),
('SEMI', '半成品', '2', 1),
('FINISHED', '成品', '3', 1),
('PACKAGE', '包装材料', '4', 1),
('TOOL', '工具', '5', 1);

-- 插入默认通知模板
INSERT INTO sys_notification_templates (template_code, template_name, template_type, subject_template, content_template, variables, created_by) VALUES
('USER_REGISTER', '用户注册通知', 'email', '欢迎注册${systemName}', '亲爱的${userName}，欢迎您注册${systemName}！', '{"systemName": "系统名称", "userName": "用户名"}', 1),
('PASSWORD_RESET', '密码重置通知', 'email', '密码重置验证码', '您的密码重置验证码是：${verifyCode}，有效期5分钟。', '{"verifyCode": "验证码"}', 1),
('DOCUMENT_APPROVED', '单据审批通知', 'email', '单据审批通知', '您的${documentType}${documentNumber}已审批通过。', '{"documentType": "单据类型", "documentNumber": "单据编号"}', 1),
('SYNC_FAILED', '同步失败通知', 'email', '数据同步失败', '${dataType}数据同步失败，错误信息：${errorMessage}', '{"dataType": "数据类型", "errorMessage": "错误信息"}', 1);

-- 创建默认管理员用户（密码：admin123，实际使用时应修改）
INSERT INTO sys_users (username, email, password_hash, salt, real_name, status, created_by) VALUES
('admin', '<EMAIL>', 'hashed_password_here', 'salt_here', '系统管理员', 1, 1);

-- 为管理员分配超级管理员角色
INSERT INTO sys_user_roles (user_id, role_id, created_by) VALUES
(1, 1, 1);

-- 为超级管理员角色分配所有权限
INSERT INTO sys_role_permissions (role_id, permission_id, created_by)
SELECT 1, id, 1 FROM sys_permissions;

-- =============================================================================
-- 视图创建
-- =============================================================================

-- 用户权限视图
CREATE VIEW v_user_permissions AS
SELECT DISTINCT
    u.id as user_id,
    u.username,
    p.permission_code,
    p.permission_name,
    p.resource_type,
    p.resource_path
FROM sys_users u
JOIN sys_user_roles ur ON u.id = ur.user_id
JOIN sys_roles r ON ur.role_id = r.id
JOIN sys_role_permissions rp ON r.id = rp.role_id
JOIN sys_permissions p ON rp.permission_id = p.id
WHERE u.deleted = FALSE AND u.status = 1
AND r.deleted = FALSE AND r.status = 1
AND p.deleted = FALSE AND p.status = 1;

-- 组织架构树形视图
CREATE VIEW v_organization_tree AS
WITH RECURSIVE org_tree AS (
    -- 根节点
    SELECT id, org_code, org_name, parent_id, 0 as level, 
           ARRAY[id] as path, org_name as full_path
    FROM biz_organizations 
    WHERE parent_id IS NULL AND deleted = FALSE
    
    UNION ALL
    
    -- 子节点
    SELECT o.id, o.org_code, o.org_name, o.parent_id, ot.level + 1,
           ot.path || o.id, ot.full_path || ' > ' || o.org_name
    FROM biz_organizations o
    JOIN org_tree ot ON o.parent_id = ot.id
    WHERE o.deleted = FALSE
)
SELECT * FROM org_tree ORDER BY path;

-- 物料库存视图（如果有库存表的话）
-- CREATE VIEW v_material_inventory AS ...

-- 单据统计视图
CREATE VIEW v_document_statistics AS
SELECT 
    dt.type_name,
    d.status,
    COUNT(*) as document_count,
    SUM(d.net_amount) as total_amount,
    DATE_TRUNC('month', d.document_date) as month_year
FROM biz_documents d
JOIN biz_document_types dt ON d.document_type_id = dt.id
WHERE d.deleted = FALSE
GROUP BY dt.type_name, d.status, DATE_TRUNC('month', d.document_date);

-- 同步状态统计视图
CREATE VIEW v_sync_statistics AS
SELECT 
    i.integration_name,
    sl.data_type,
    sl.sync_status,
    COUNT(*) as sync_count,
    AVG(EXTRACT(EPOCH FROM (sl.end_time - sl.start_time))) as avg_duration_seconds,
    DATE_TRUNC('day', sl.start_time) as sync_date
FROM sys_sync_logs sl
JOIN sys_integrations i ON sl.integration_id = i.id
WHERE sl.end_time IS NOT NULL
GROUP BY i.integration_name, sl.data_type, sl.sync_status, DATE_TRUNC('day', sl.start_time);

-- =============================================================================
-- 注释
-- =============================================================================

COMMENT ON DATABASE kingdee_integration_platform IS '金蝶云星辰业务扩展与集成平台数据库';

-- 表注释
COMMENT ON TABLE sys_users IS '系统用户表';
COMMENT ON TABLE sys_roles IS '系统角色表';
COMMENT ON TABLE sys_permissions IS '系统权限表';
COMMENT ON TABLE biz_organizations IS '组织架构表';
COMMENT ON TABLE biz_materials IS '物料主数据表';
COMMENT ON TABLE biz_customers IS '客户主数据表';
COMMENT ON TABLE biz_suppliers IS '供应商主数据表';
COMMENT ON TABLE biz_documents IS '业务单据主表';
COMMENT ON TABLE biz_document_items IS '业务单据明细表';
COMMENT ON TABLE biz_workflows IS '工作流定义表';
COMMENT ON TABLE biz_workflow_instances IS '工作流实例表';
COMMENT ON TABLE sys_integrations IS '系统集成配置表';
COMMENT ON TABLE sys_sync_logs IS '数据同步日志表';
COMMENT ON TABLE sys_api_logs IS 'API调用日志表';
COMMENT ON TABLE sys_notifications IS '通知记录表';
COMMENT ON TABLE sys_files IS '文件信息表';
COMMENT ON TABLE sys_audit_logs IS '操作审计日志表';

-- 字段注释示例（部分重要字段）
COMMENT ON COLUMN sys_users.password_hash IS '密码哈希值';
COMMENT ON COLUMN sys_users.salt IS '密码盐值';
COMMENT ON COLUMN sys_users.mfa_enabled IS '是否启用多因素认证';
COMMENT ON COLUMN sys_users.mfa_secret IS '多因素认证密钥';
COMMENT ON COLUMN biz_materials.external_id IS '外部系统ID（如金蝶系统中的物料ID）';
COMMENT ON COLUMN biz_materials.sync_status IS '同步状态：pending-待同步，synced-已同步，failed-同步失败';
COMMENT ON COLUMN biz_documents.workflow_instance_id IS '关联的工作流实例ID';
COMMENT ON COLUMN sys_integrations.app_secret IS '应用密钥（加密存储）';
COMMENT ON COLUMN sys_sync_logs.sync_direction IS '同步方向：import-导入，export-导出，bidirectional-双向';

-- 完成数据库初始化
SELECT 'Database schema created successfully!' as result;