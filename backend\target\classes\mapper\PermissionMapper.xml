<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kingdee.integration.mapper.PermissionMapper">

    <!-- 权限结果映射 -->
    <resultMap id="BaseResultMap" type="com.kingdee.integration.entity.Permission">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="permission_code" property="permissionCode" jdbcType="VARCHAR"/>
        <result column="permission_name" property="permissionName" jdbcType="VARCHAR"/>
        <result column="permission_type" property="permissionType" jdbcType="VARCHAR"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="permission_url" property="permissionUrl" jdbcType="VARCHAR"/>
        <result column="http_method" property="httpMethod" jdbcType="VARCHAR"/>
        <result column="icon" property="icon" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="permission_path" property="permissionPath" jdbcType="VARCHAR"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="created_time" property="createdTime" jdbcType="TIMESTAMP"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="updated_time" property="updatedTime" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 权限详情结果映射（含父权限信息） -->
    <resultMap id="DetailResultMap" type="com.kingdee.integration.entity.Permission" extends="BaseResultMap">
        <result column="parent_permission_name" property="parentPermissionName" jdbcType="VARCHAR"/>
        <collection property="children" ofType="com.kingdee.integration.entity.Permission">
            <id column="child_id" property="id" jdbcType="BIGINT"/>
            <result column="child_permission_code" property="permissionCode" jdbcType="VARCHAR"/>
            <result column="child_permission_name" property="permissionName" jdbcType="VARCHAR"/>
            <result column="child_permission_type" property="permissionType" jdbcType="VARCHAR"/>
            <result column="child_sort_order" property="sortOrder" jdbcType="INTEGER"/>
            <result column="child_status" property="status" jdbcType="INTEGER"/>
        </collection>
    </resultMap>

    <!-- 基础列定义 -->
    <sql id="Base_Column_List">
        id, permission_code, permission_name, permission_type, parent_id, permission_url,
        http_method, icon, sort_order, description, status, permission_path,
        created_by, created_time, updated_by, updated_time, version, deleted
    </sql>

    <!-- 根据权限编码查询权限 -->
    <select id="selectByPermissionCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_code = #{permissionCode}
        AND deleted = 0
    </select>

    <!-- 根据权限名称查询权限 -->
    <select id="selectByPermissionName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_name = #{permissionName}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        AND deleted = 0
    </select>

    <!-- 分页查询权限列表 -->
    <select id="selectPermissionPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE deleted = 0
        <if test="permissionCode != null and permissionCode != ''">
            AND permission_code LIKE CONCAT('%', #{permissionCode}, '%')
        </if>
        <if test="permissionName != null and permissionName != ''">
            AND permission_name LIKE CONCAT('%', #{permissionName}, '%')
        </if>
        <if test="permissionType != null and permissionType != ''">
            AND permission_type = #{permissionType}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY sort_order ASC, created_time DESC
    </select>

    <!-- 获取权限树结构 -->
    <select id="selectPermissionTree" resultMap="DetailResultMap">
        SELECT 
            p.id, p.permission_code, p.permission_name, p.permission_type, p.parent_id,
            p.permission_url, p.http_method, p.icon, p.sort_order, p.description, p.status,
            p.permission_path, p.created_by, p.created_time, p.updated_by, p.updated_time,
            p.version, p.deleted,
            pp.permission_name as parent_permission_name,
            c.id as child_id, c.permission_code as child_permission_code,
            c.permission_name as child_permission_name, c.permission_type as child_permission_type,
            c.sort_order as child_sort_order, c.status as child_status
        FROM sys_permission p
        LEFT JOIN sys_permission pp ON p.parent_id = pp.id AND pp.deleted = 0
        LEFT JOIN sys_permission c ON p.id = c.parent_id AND c.deleted = 0
        WHERE p.deleted = 0
        <if test="permissionType != null and permissionType != ''">
            AND p.permission_type = #{permissionType}
        </if>
        <if test="enabledOnly != null and enabledOnly">
            AND p.status = 1
        </if>
        ORDER BY p.sort_order ASC, c.sort_order ASC
    </select>

    <!-- 获取启用的权限列表 -->
    <select id="selectEnabledPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE status = 1 AND deleted = 0
        <if test="permissionType != null and permissionType != ''">
            AND permission_type = #{permissionType}
        </if>
        ORDER BY sort_order ASC
    </select>

    <!-- 根据权限类型查询权限列表 -->
    <select id="selectByPermissionType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_type = #{permissionType}
        AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 根据角色ID查询权限列表 -->
    <select id="selectByRoleId" resultMap="BaseResultMap">
        SELECT DISTINCT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
        AND rp.status = 1
        AND rp.deleted = 0
        AND p.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 根据用户ID查询权限列表 -->
    <select id="selectByUserId" resultMap="BaseResultMap">
        SELECT DISTINCT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND ur.status = 1
        AND ur.deleted = 0
        AND rp.status = 1
        AND rp.deleted = 0
        AND p.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 获取菜单权限列表 -->
    <select id="selectMenuPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_type = 'MENU'
        AND deleted = 0
        <if test="enabledOnly != null and enabledOnly">
            AND status = 1
        </if>
        ORDER BY sort_order ASC
    </select>

    <!-- 获取按钮权限列表 -->
    <select id="selectButtonPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_type = 'BUTTON'
        AND deleted = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="enabledOnly != null and enabledOnly">
            AND status = 1
        </if>
        ORDER BY sort_order ASC
    </select>

    <!-- 获取API权限列表 -->
    <select id="selectApiPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_type = 'API'
        AND deleted = 0
        <if test="enabledOnly != null and enabledOnly">
            AND status = 1
        </if>
        ORDER BY sort_order ASC
    </select>

    <!-- 获取子权限列表 -->
    <select id="selectChildPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE parent_id = #{parentId}
        AND deleted = 0
        <if test="enabledOnly != null and enabledOnly">
            AND status = 1
        </if>
        ORDER BY sort_order ASC
    </select>

    <!-- 获取权限路径 -->
    <select id="selectPermissionPath" resultType="java.lang.String">
        SELECT permission_path
        FROM sys_permission
        WHERE id = #{id}
        AND deleted = 0
    </select>

    <!-- 检查权限编码是否存在 -->
    <select id="existsByPermissionCode" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_permission
        WHERE permission_code = #{permissionCode}
        <if test="excludePermissionId != null">
            AND id != #{excludePermissionId}
        </if>
        AND deleted = 0
    </select>

    <!-- 检查权限名称是否存在 -->
    <select id="existsByPermissionName" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_permission
        WHERE permission_name = #{permissionName}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
        <if test="excludePermissionId != null">
            AND id != #{excludePermissionId}
        </if>
        AND deleted = 0
    </select>

    <!-- 检查权限URL是否存在 -->
    <select id="existsByPermissionUrl" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM sys_permission
        WHERE permission_url = #{permissionUrl}
        <if test="httpMethod != null and httpMethod != ''">
            AND http_method = #{httpMethod}
        </if>
        <if test="excludePermissionId != null">
            AND id != #{excludePermissionId}
        </if>
        AND deleted = 0
    </select>

    <!-- 统计权限数量 -->
    <select id="countPermissions" resultType="java.lang.Long">
        SELECT COUNT(1)
        FROM sys_permission
        WHERE deleted = 0
        <if test="permissionType != null and permissionType != ''">
            AND permission_type = #{permissionType}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
    </select>

    <!-- 获取最大排序号 -->
    <select id="selectMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM sys_permission
        WHERE deleted = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND parent_id IS NULL
        </if>
    </select>

    <!-- 批量更新权限状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_permission
        SET status = #{status}, updated_time = NOW()
        WHERE id IN
        <foreach collection="permissionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND deleted = 0
    </update>

    <!-- 获取权限详情（包含父权限信息） -->
    <select id="selectPermissionDetail" resultMap="DetailResultMap">
        SELECT 
            p.id, p.permission_code, p.permission_name, p.permission_type, p.parent_id,
            p.permission_url, p.http_method, p.icon, p.sort_order, p.description, p.status,
            p.permission_path, p.created_by, p.created_time, p.updated_by, p.updated_time,
            p.version, p.deleted,
            pp.permission_name as parent_permission_name
        FROM sys_permission p
        LEFT JOIN sys_permission pp ON p.parent_id = pp.id AND pp.deleted = 0
        WHERE p.id = #{id}
        AND p.deleted = 0
    </select>

    <!-- 检查权限是否被使用 -->
    <select id="isPermissionInUse" resultType="java.lang.Boolean">
        SELECT COUNT(1) > 0
        FROM (
            SELECT 1 FROM sys_role_permission WHERE permission_id = #{id} AND deleted = 0
            UNION ALL
            SELECT 1 FROM sys_permission WHERE parent_id = #{id} AND deleted = 0
        ) t
    </select>

    <!-- 根据URL和方法查询权限 -->
    <select id="selectByPermissionUrl" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_url = #{permissionUrl}
        <if test="httpMethod != null and httpMethod != ''">
            AND http_method = #{httpMethod}
        </if>
        AND deleted = 0
        LIMIT 1
    </select>

    <!-- 获取用户菜单树 -->
    <select id="selectUserMenuTree" resultMap="DetailResultMap">
        SELECT DISTINCT
            p.id, p.permission_code, p.permission_name, p.permission_type, p.parent_id,
            p.permission_url, p.http_method, p.icon, p.sort_order, p.description, p.status,
            p.permission_path,
            pp.permission_name as parent_permission_name
        FROM sys_permission p
        LEFT JOIN sys_permission pp ON p.parent_id = pp.id AND pp.deleted = 0
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND p.permission_type = 'MENU'
        AND p.status = 1
        AND p.deleted = 0
        AND ur.status = 1
        AND ur.deleted = 0
        AND rp.status = 1
        AND rp.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 获取用户按钮权限 -->
    <select id="selectUserButtonPermissions" resultType="java.lang.String">
        SELECT DISTINCT p.permission_code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND p.permission_type = 'BUTTON'
        AND p.status = 1
        AND p.deleted = 0
        AND ur.status = 1
        AND ur.deleted = 0
        AND rp.status = 1
        AND rp.deleted = 0
    </select>

    <!-- 获取用户API权限 -->
    <select id="selectUserApiPermissions" resultType="java.lang.String">
        SELECT DISTINCT p.permission_code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        INNER JOIN sys_user_role ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND p.permission_type = 'API'
        AND p.status = 1
        AND p.deleted = 0
        AND ur.status = 1
        AND ur.deleted = 0
        AND rp.status = 1
        AND rp.deleted = 0
    </select>

    <!-- 根据角色ID列表查询权限 -->
    <select id="selectByRoleIds" resultMap="BaseResultMap">
        SELECT DISTINCT p.*
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id IN
        <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
            #{roleId}
        </foreach>
        AND rp.status = 1
        AND rp.deleted = 0
        AND p.deleted = 0
        ORDER BY p.sort_order ASC
    </select>

    <!-- 获取系统权限列表 -->
    <select id="selectSystemPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_code LIKE 'SYS_%'
        AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 获取业务权限列表 -->
    <select id="selectBusinessPermissions" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_permission
        WHERE permission_code NOT LIKE 'SYS_%'
        AND deleted = 0
        ORDER BY sort_order ASC
    </select>

    <!-- 更新权限路径 -->
    <update id="updatePermissionPath">
        UPDATE sys_permission
        SET permission_path = #{permissionPath}, updated_time = NOW()
        WHERE id = #{id}
        AND deleted = 0
    </update>

    <!-- 批量更新子权限路径 -->
    <update id="batchUpdateChildPaths">
        UPDATE sys_permission
        SET permission_path = CONCAT(#{newParentPath}, '/', permission_code),
            updated_time = NOW()
        WHERE parent_id = #{parentId}
        AND deleted = 0
    </update>

</mapper>