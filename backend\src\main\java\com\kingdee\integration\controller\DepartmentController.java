package com.kingdee.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.entity.Department;
import com.kingdee.integration.service.DepartmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 部门管理控制器
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Tag(name = "部门管理", description = "部门管理相关接口")
@RestController
@RequestMapping("/api/departments")
@Validated
public class DepartmentController {

    @Autowired
    private DepartmentService departmentService;

    /**
     * 分页查询部门列表
     */
    @Operation(summary = "分页查询部门列表", description = "根据条件分页查询部门列表")
    @GetMapping("/page")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<IPage<Department>> pageDepartments(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") Integer current,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") Integer size,
            @Parameter(description = "部门名称") @RequestParam(required = false) String deptName,
            @Parameter(description = "部门编码") @RequestParam(required = false) String deptCode,
            @Parameter(description = "部门类型") @RequestParam(required = false) String deptType,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "父部门ID") @RequestParam(required = false) Long parentId) {
        Page<Department> page = new Page<>(current, size);
        IPage<Department> result = departmentService.pageDepartments(page, deptName, deptCode, deptType, status, parentId);
        return Result.success(result);
    }

    /**
     * 获取部门树结构
     */
    @Operation(summary = "获取部门树结构", description = "获取完整的部门树结构")
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getDepartmentTree() {
        List<Department> tree = departmentService.getDepartmentTree();
        return Result.success(tree);
    }

    /**
     * 查询部门详情
     */
    @Operation(summary = "查询部门详情", description = "根据部门ID查询部门详情")
    @GetMapping("/{deptId}")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Department> getDepartmentDetail(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        Department department = departmentService.getById(deptId);
        return Result.success(department);
    }

    /**
     * 查询部门详情（包含父部门信息）
     */
    @Operation(summary = "查询部门详情（包含父部门信息）", description = "根据部门ID查询部门详情，包含父部门信息")
    @GetMapping("/{deptId}/with-parent")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Department> getDepartmentWithParent(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        Department department = departmentService.getDepartmentWithParent(deptId);
        return Result.success(department);
    }

    /**
     * 根据部门编码查询部门
     */
    @Operation(summary = "根据部门编码查询部门", description = "根据部门编码查询部门信息")
    @GetMapping("/code/{deptCode}")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Department> getDepartmentByCode(
            @Parameter(description = "部门编码") @PathVariable @NotBlank String deptCode) {
        Department department = departmentService.getByDeptCode(deptCode);
        return Result.success(department);
    }

    /**
     * 创建部门
     */
    @Operation(summary = "创建部门", description = "创建新的部门")
    @PostMapping
    @PreAuthorize("hasAuthority('dept:create')")
    public Result<Boolean> createDepartment(@Valid @RequestBody Department department) {
        boolean success = departmentService.createDepartment(department);
        return Result.success(success);
    }

    /**
     * 更新部门
     */
    @Operation(summary = "更新部门", description = "更新部门信息")
    @PutMapping("/{deptId}")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> updateDepartment(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId,
            @Valid @RequestBody Department department) {
        department.setDeptId(deptId);
        boolean success = departmentService.updateDepartment(department);
        return Result.success(success);
    }

    /**
     * 删除部门
     */
    @Operation(summary = "删除部门", description = "根据部门ID删除部门")
    @DeleteMapping("/{deptId}")
    @PreAuthorize("hasAuthority('dept:delete')")
    public Result<Boolean> deleteDepartment(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean success = departmentService.deleteDepartment(deptId);
        return Result.success(success);
    }

    /**
     * 批量删除部门
     */
    @Operation(summary = "批量删除部门", description = "根据部门ID列表批量删除部门")
    @DeleteMapping("/batch")
    @PreAuthorize("hasAuthority('dept:delete')")
    public Result<Boolean> deleteBatchDepartments(
            @Parameter(description = "部门ID列表") @RequestBody @NotEmpty List<Long> deptIds) {
        boolean success = departmentService.deleteBatchDepartments(deptIds);
        return Result.success(success);
    }

    /**
     * 启用部门
     */
    @Operation(summary = "启用部门", description = "启用指定的部门")
    @PutMapping("/{deptId}/enable")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> enableDepartment(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean success = departmentService.enableDepartment(deptId);
        return Result.success(success);
    }

    /**
     * 禁用部门
     */
    @Operation(summary = "禁用部门", description = "禁用指定的部门")
    @PutMapping("/{deptId}/disable")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> disableDepartment(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean success = departmentService.disableDepartment(deptId);
        return Result.success(success);
    }

    /**
     * 批量更新部门状态
     */
    @Operation(summary = "批量更新部门状态", description = "批量更新部门状态")
    @PutMapping("/batch/status")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> batchUpdateStatus(
            @Parameter(description = "部门ID列表") @RequestParam @NotEmpty List<Long> deptIds,
            @Parameter(description = "状态") @RequestParam @NotNull Integer status) {
        boolean success = departmentService.batchUpdateStatus(deptIds, status);
        return Result.success(success);
    }

    /**
     * 移动部门
     */
    @Operation(summary = "移动部门", description = "将部门移动到新的父部门下")
    @PutMapping("/{deptId}/move")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> moveDepartment(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId,
            @Parameter(description = "新父部门ID") @RequestParam Long newParentId) {
        boolean success = departmentService.moveDepartment(deptId, newParentId);
        return Result.success(success);
    }

    /**
     * 获取启用的部门列表
     */
    @Operation(summary = "获取启用的部门列表", description = "获取所有启用状态的部门列表")
    @GetMapping("/enabled")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getEnabledDepartments() {
        List<Department> departments = departmentService.getEnabledDepartments();
        return Result.success(departments);
    }

    /**
     * 根据部门类型查询部门列表
     */
    @Operation(summary = "根据部门类型查询部门列表", description = "根据部门类型查询部门列表")
    @GetMapping("/type/{deptType}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getDepartmentsByType(
            @Parameter(description = "部门类型") @PathVariable @NotBlank String deptType) {
        List<Department> departments = departmentService.getDepartmentsByType(deptType);
        return Result.success(departments);
    }

    /**
     * 根据用户ID查询部门
     */
    @Operation(summary = "根据用户ID查询部门", description = "根据用户ID查询所属部门")
    @GetMapping("/user/{userId}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<Department> getDepartmentByUserId(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        Department department = departmentService.getDepartmentByUserId(userId);
        return Result.success(department);
    }

    /**
     * 根据负责人ID查询部门列表
     */
    @Operation(summary = "根据负责人ID查询部门列表", description = "根据负责人ID查询管理的部门列表")
    @GetMapping("/manager/{managerId}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getDepartmentsByManagerId(
            @Parameter(description = "负责人ID") @PathVariable @NotNull Long managerId) {
        List<Department> departments = departmentService.getDepartmentsByManagerId(managerId);
        return Result.success(departments);
    }

    /**
     * 获取子部门列表
     */
    @Operation(summary = "获取子部门列表", description = "获取指定部门的直接子部门列表")
    @GetMapping("/{parentId}/children")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getChildDepartments(
            @Parameter(description = "父部门ID") @PathVariable @NotNull Long parentId) {
        List<Department> departments = departmentService.getChildDepartments(parentId);
        return Result.success(departments);
    }

    /**
     * 获取所有子部门ID
     */
    @Operation(summary = "获取所有子部门ID", description = "获取指定部门的所有子部门ID（包括子子部门）")
    @GetMapping("/{parentId}/all-children-ids")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Long>> getAllChildDepartmentIds(
            @Parameter(description = "父部门ID") @PathVariable @NotNull Long parentId) {
        List<Long> deptIds = departmentService.getAllChildDepartmentIds(parentId);
        return Result.success(deptIds);
    }

    /**
     * 获取部门路径
     */
    @Operation(summary = "获取部门路径", description = "获取指定部门的完整路径")
    @GetMapping("/{deptId}/path")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<String> getDepartmentPath(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        String path = departmentService.getDepartmentPath(deptId);
        return Result.success(path);
    }

    /**
     * 检查部门编码是否存在
     */
    @Operation(summary = "检查部门编码是否存在", description = "检查指定的部门编码是否已存在")
    @GetMapping("/check/code/{deptCode}")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Boolean> isDeptCodeExists(
            @Parameter(description = "部门编码") @PathVariable @NotBlank String deptCode) {
        boolean exists = departmentService.isDeptCodeExists(deptCode);
        return Result.success(exists);
    }

    /**
     * 检查部门名称是否存在
     */
    @Operation(summary = "检查部门名称是否存在", description = "检查指定的部门名称在同一父部门下是否已存在")
    @GetMapping("/check/name")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Boolean> isDeptNameExists(
            @Parameter(description = "部门名称") @RequestParam @NotBlank String deptName,
            @Parameter(description = "父部门ID") @RequestParam Long parentId) {
        boolean exists = departmentService.isDeptNameExists(deptName, parentId);
        return Result.success(exists);
    }

    /**
     * 统计部门数量
     */
    @Operation(summary = "统计部门数量", description = "统计系统中的部门总数")
    @GetMapping("/count")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<Long> countDepartments() {
        Long count = departmentService.countDepartments();
        return Result.success(count);
    }

    /**
     * 根据类型统计部门数量
     */
    @Operation(summary = "根据类型统计部门数量", description = "根据部门类型统计部门数量")
    @GetMapping("/count/type/{deptType}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<Long> countDepartmentsByType(
            @Parameter(description = "部门类型") @PathVariable @NotBlank String deptType) {
        Long count = departmentService.countDepartmentsByType(deptType);
        return Result.success(count);
    }

    /**
     * 根据状态统计部门数量
     */
    @Operation(summary = "根据状态统计部门数量", description = "根据部门状态统计部门数量")
    @GetMapping("/count/status/{status}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<Long> countDepartmentsByStatus(
            @Parameter(description = "状态") @PathVariable @NotNull Integer status) {
        Long count = departmentService.countDepartmentsByStatus(status);
        return Result.success(count);
    }

    /**
     * 获取最大排序号
     */
    @Operation(summary = "获取最大排序号", description = "获取指定父部门下的最大排序号")
    @GetMapping("/max-sort-order")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Integer> getMaxSortOrder(
            @Parameter(description = "父部门ID") @RequestParam Long parentId) {
        Integer maxSortOrder = departmentService.getMaxSortOrder(parentId);
        return Result.success(maxSortOrder);
    }

    /**
     * 检查部门是否有子部门
     */
    @Operation(summary = "检查部门是否有子部门", description = "检查指定部门是否有子部门")
    @GetMapping("/{deptId}/has-children")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Boolean> hasChildDepartments(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean hasChildren = departmentService.hasChildDepartments(deptId);
        return Result.success(hasChildren);
    }

    /**
     * 检查部门是否有用户
     */
    @Operation(summary = "检查部门是否有用户", description = "检查指定部门是否有用户")
    @GetMapping("/{deptId}/has-users")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Boolean> hasUsers(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean hasUsers = departmentService.hasUsers(deptId);
        return Result.success(hasUsers);
    }

    /**
     * 获取部门用户数量
     */
    @Operation(summary = "获取部门用户数量", description = "获取指定部门的用户数量")
    @GetMapping("/{deptId}/user-count")
    @PreAuthorize("hasAuthority('dept:detail')")
    public Result<Long> getDepartmentUserCount(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        Long userCount = departmentService.getDepartmentUserCount(deptId);
        return Result.success(userCount);
    }

    /**
     * 更新部门路径
     */
    @Operation(summary = "更新部门路径", description = "更新指定部门的路径信息")
    @PutMapping("/{deptId}/update-path")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> updateDepartmentPath(
            @Parameter(description = "部门ID") @PathVariable @NotNull Long deptId) {
        boolean success = departmentService.updateDepartmentPath(deptId);
        return Result.success(success);
    }

    /**
     * 批量更新子部门路径
     */
    @Operation(summary = "批量更新子部门路径", description = "批量更新指定父部门下所有子部门的路径")
    @PutMapping("/{parentId}/batch-update-child-paths")
    @PreAuthorize("hasAuthority('dept:update')")
    public Result<Boolean> batchUpdateChildDepartmentPaths(
            @Parameter(description = "父部门ID") @PathVariable @NotNull Long parentId) {
        boolean success = departmentService.batchUpdateChildDepartmentPaths(parentId);
        return Result.success(success);
    }

    /**
     * 获取根部门列表
     */
    @Operation(summary = "获取根部门列表", description = "获取所有根部门列表")
    @GetMapping("/root")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getRootDepartments() {
        List<Department> departments = departmentService.getRootDepartments();
        return Result.success(departments);
    }

    /**
     * 获取叶子部门列表
     */
    @Operation(summary = "获取叶子部门列表", description = "获取所有叶子部门列表")
    @GetMapping("/leaf")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getLeafDepartments() {
        List<Department> departments = departmentService.getLeafDepartments();
        return Result.success(departments);
    }

    /**
     * 根据层级查询部门列表
     */
    @Operation(summary = "根据层级查询部门列表", description = "根据部门层级查询部门列表")
    @GetMapping("/level/{deptLevel}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getDepartmentsByLevel(
            @Parameter(description = "部门层级") @PathVariable @NotNull Integer deptLevel) {
        List<Department> departments = departmentService.getDepartmentsByLevel(deptLevel);
        return Result.success(departments);
    }

    /**
     * 搜索部门
     */
    @Operation(summary = "搜索部门", description = "根据关键词搜索部门（支持模糊查询）")
    @GetMapping("/search")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> searchDepartments(
            @Parameter(description = "搜索关键词") @RequestParam @NotBlank String keyword) {
        List<Department> departments = departmentService.searchDepartments(keyword);
        return Result.success(departments);
    }

    /**
     * 获取用户可访问的部门列表
     */
    @Operation(summary = "获取用户可访问的部门列表", description = "获取指定用户可访问的部门列表")
    @GetMapping("/accessible/{userId}")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<List<Department>> getAccessibleDepartments(
            @Parameter(description = "用户ID") @PathVariable @NotNull Long userId) {
        List<Department> departments = departmentService.getAccessibleDepartments(userId);
        return Result.success(departments);
    }

    /**
     * 获取部门统计信息
     */
    @Operation(summary = "获取部门统计信息", description = "获取部门的各种统计信息")
    @GetMapping("/statistics")
    @PreAuthorize("hasAuthority('dept:list')")
    public Result<Map<String, Object>> getDepartmentStatistics() {
        Map<String, Object> statistics = departmentService.getDepartmentStatistics();
        return Result.success(statistics);
    }

    /**
     * 复制部门
     */
    @Operation(summary = "复制部门", description = "复制现有部门创建新部门")
    @PostMapping("/{sourceDeptId}/copy")
    @PreAuthorize("hasAuthority('dept:create')")
    public Result<Department> copyDepartment(
            @Parameter(description = "源部门ID") @PathVariable @NotNull Long sourceDeptId,
            @Parameter(description = "新部门编码") @RequestParam @NotBlank String newDeptCode,
            @Parameter(description = "新部门名称") @RequestParam @NotBlank String newDeptName,
            @Parameter(description = "新父部门ID") @RequestParam Long newParentId) {
        Department department = departmentService.copyDepartment(sourceDeptId, newDeptCode, newDeptName, newParentId);
        return Result.success(department);
    }

    /**
     * 导入部门
     */
    @Operation(summary = "导入部门", description = "批量导入部门数据")
    @PostMapping("/import")
    @PreAuthorize("hasAuthority('dept:import')")
    public Result<DepartmentService.ImportResult> importDepartments(
            @Parameter(description = "部门列表") @RequestBody @NotEmpty List<Department> departments) {
        DepartmentService.ImportResult result = departmentService.importDepartments(departments);
        return Result.success(result);
    }

    /**
     * 导出部门
     */
    @Operation(summary = "导出部门", description = "导出指定的部门数据")
    @PostMapping("/export")
    @PreAuthorize("hasAuthority('dept:export')")
    public Result<List<Department>> exportDepartments(
            @Parameter(description = "部门ID列表") @RequestBody List<Long> deptIds) {
        List<Department> departments = departmentService.exportDepartments(deptIds);
        return Result.success(departments);
    }

    /**
     * 同步部门层级和路径
     */
    @Operation(summary = "同步部门层级和路径", description = "同步所有部门的层级和路径信息")
    @PutMapping("/sync-hierarchy")
    @PreAuthorize("hasAuthority('dept:sync')")
    public Result<Boolean> syncDepartmentHierarchy() {
        boolean success = departmentService.syncDepartmentHierarchy();
        return Result.success(success);
    }

    /**
     * 清空缓存
     */
    @Operation(summary = "清空缓存", description = "清空部门相关的所有缓存")
    @DeleteMapping("/cache")
    @PreAuthorize("hasAuthority('dept:cache')")
    public Result<Void> clearCache() {
        departmentService.clearCache();
        return Result.success();
    }

    /**
     * 同步缓存
     */
    @Operation(summary = "同步缓存", description = "同步部门相关的缓存数据")
    @PutMapping("/cache")
    @PreAuthorize("hasAuthority('dept:cache')")
    public Result<Void> syncCache() {
        departmentService.syncCache();
        return Result.success();
    }
}