package com.kingdee.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kingdee.integration.entity.User;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface UserService extends IService<User> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User findByUsername(String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    User findByEmail(String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User findByPhone(String phone);

    /**
     * 根据员工编号查询用户
     *
     * @param employeeNo 员工编号
     * @return 用户信息
     */
    User findByEmployeeNo(String employeeNo);

    /**
     * 分页查询用户列表
     *
     * @param page 分页参数
     * @param username 用户名（模糊查询）
     * @param realName 真实姓名（模糊查询）
     * @param email 邮箱（模糊查询）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 用户分页列表
     */
    IPage<User> pageUsers(Page<User> page, String username, String realName, 
                         String email, Long departmentId, Integer status);

    /**
     * 创建用户
     *
     * @param user 用户信息
     * @return 创建的用户
     */
    User createUser(User user);

    /**
     * 更新用户
     *
     * @param user 用户信息
     * @return 更新的用户
     */
    User updateUser(User user);

    /**
     * 删除用户
     *
     * @param userId 用户ID
     * @return 是否删除成功
     */
    boolean deleteUser(Long userId);

    /**
     * 批量删除用户
     *
     * @param userIds 用户ID列表
     * @return 是否删除成功
     */
    boolean deleteUsers(List<Long> userIds);

    /**
     * 启用用户
     *
     * @param userId 用户ID
     * @return 是否启用成功
     */
    boolean enableUser(Long userId);

    /**
     * 禁用用户
     *
     * @param userId 用户ID
     * @return 是否禁用成功
     */
    boolean disableUser(Long userId);

    /**
     * 锁定用户
     *
     * @param userId 用户ID
     * @return 是否锁定成功
     */
    boolean lockUser(Long userId);

    /**
     * 解锁用户
     *
     * @param userId 用户ID
     * @return 是否解锁成功
     */
    boolean unlockUser(Long userId);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码
     * @return 是否重置成功
     */
    boolean resetPassword(Long userId, String newPassword);

    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return 是否修改成功
     */
    boolean changePassword(Long userId, String oldPassword, String newPassword);

    /**
     * 验证用户密码
     *
     * @param user 用户信息
     * @param rawPassword 原始密码
     * @return 是否验证成功
     */
    boolean validatePassword(User user, String rawPassword);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginIp 登录IP
     * @return 是否更新成功
     */
    boolean updateLastLoginInfo(Long userId, String loginIp);

    /**
     * 增加登录失败次数
     *
     * @param userId 用户ID
     * @return 是否更新成功
     */
    boolean incrementLoginFailureCount(Long userId);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> getUsersByRoleId(Long roleId);

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @param includeSubDept 是否包含子部门
     * @return 用户列表
     */
    List<User> getUsersByDepartmentId(Long departmentId, Boolean includeSubDept);

    /**
     * 根据权限编码查询用户列表
     *
     * @param permissionCode 权限编码
     * @return 用户列表
     */
    List<User> getUsersByPermissionCode(String permissionCode);

    /**
     * 分配用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否分配成功
     */
    boolean assignRoles(Long userId, List<Long> roleIds);

    /**
     * 移除用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 是否移除成功
     */
    boolean removeRoles(Long userId, List<Long> roleIds);

    /**
     * 获取用户角色列表
     *
     * @param userId 用户ID
     * @return 角色编码列表
     */
    List<String> getUserRoles(Long userId);

    /**
     * 获取用户权限列表
     *
     * @param userId 用户ID
     * @return 权限编码列表
     */
    List<String> getUserPermissions(Long userId);

    /**
     * 检查用户是否有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(Long userId, String permissionCode);

    /**
     * 检查用户是否有指定角色
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRole(Long userId, String roleCode);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByUsername(String username, Long excludeUserId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByEmail(String email, Long excludeUserId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByPhone(String phone, Long excludeUserId);

    /**
     * 统计用户数量
     *
     * @param status 状态（可选）
     * @param departmentId 部门ID（可选）
     * @return 用户数量
     */
    Long countUsers(Integer status, Long departmentId);

    /**
     * 导入用户
     *
     * @param users 用户列表
     * @param updateExisting 是否更新已存在的用户
     * @return 导入结果
     */
    ImportResult importUsers(List<User> users, boolean updateExisting);

    /**
     * 导出用户
     *
     * @param userIds 用户ID列表（为空则导出所有）
     * @return 用户列表
     */
    List<User> exportUsers(List<Long> userIds);

    /**
     * 导入结果类
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errorMessages;

        // 构造函数、getter、setter省略
        public ImportResult(int totalCount, int successCount, int failureCount, List<String> errorMessages) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.errorMessages = errorMessages;
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public List<String> getErrorMessages() { return errorMessages; }
    }
}