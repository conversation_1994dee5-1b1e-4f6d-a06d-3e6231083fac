# 金蝶云星辰业务扩展与集成平台配置文件
spring:
  application:
    name: kingdee-integration-platform
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      # 打开PSCache，并且指定每个连接上PSCache的大小
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,***********/16
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: 
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456
  
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: validate
      naming:
        physical-strategy: org.hibernate.boot.model.naming.PhysicalNamingStrategyStandardImpl
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true
        batch_versioned_data: true
  
  # Redis配置
  data:
    redis:
      timeout: 10s
      lettuce:
        pool:
          max-active: 200
          max-wait: -1ms
          max-idle: 10
          min-idle: 0
  
  # 邮件配置
  mail:
    default-encoding: UTF-8
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
  
  # Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false
  
  # 国际化配置
  messages:
    basename: i18n/messages
    encoding: UTF-8
    cache-duration: 3600

# MyBatis Plus配置
mybatis-plus:
  # 配置扫描通用枚举
  type-enums-package: com.kingdee.integration.common.enums
  configuration:
    # 自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 打印SQL
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      # 主键策略
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: true
      logic-not-delete-value: false
      # 字段验证策略
      insert-strategy: NOT_NULL
      update-strategy: NOT_NULL
      select-strategy: NOT_EMPTY

# 日志配置
logging:
  level:
    root: INFO
    com.kingdee.integration: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%wEx"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} %5p ${PID:- } --- [%t] %-40.40logger{39} : %m%n%wEx"
  file:
    name: logs/kingdee-integration-platform.log
    max-size: 100MB
    max-history: 30

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus,env,configprops,beans,mappings
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
  metrics:
    export:
      prometheus:
        enabled: true

# 应用信息
info:
  app:
    name: ${spring.application.name}
    description: 金蝶云星辰业务扩展与集成平台
    version: 1.0.0
    encoding: UTF-8
    java:
      version: ${java.version}

# 自定义配置
app:
  # JWT配置
  jwt:
    secret: kingdee-integration-platform-jwt-secret-key-2024
    expiration: 86400 # 24小时
    refresh-expiration: 604800 # 7天
    header: Authorization
    prefix: Bearer
  
  # 安全配置
  security:
    # 密码加密盐值
    password-salt: kingdee-integration-platform-salt
    # 登录失败最大次数
    max-login-attempts: 5
    # 账户锁定时间（分钟）
    account-lock-duration: 30
    # 密码最小长度
    password-min-length: 8
    # 密码复杂度要求
    password-complexity: true
  
  # 文件存储配置
  file:
    # 存储类型：local, minio, oss, cos
    storage-type: local
    # 本地存储路径
    local-path: ./uploads
    # 允许的文件类型
    allowed-types: jpg,jpeg,png,gif,bmp,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar
    # 最大文件大小（MB）
    max-size: 100
  
  # MinIO配置
  minio:
    endpoint: http://localhost:9000
    access-key: minioadmin
    secret-key: minioadmin
    bucket-name: kingdee-integration
  
  # 同步配置
  sync:
    # 批次大小
    batch-size: 100
    # 重试次数
    retry-times: 3
    # 超时时间（秒）
    timeout: 300
    # 并发线程数
    thread-pool-size: 10
  
  # 通知配置
  notification:
    # 邮件通知
    email:
      enabled: true
      template-path: templates/email
    # 短信通知
    sms:
      enabled: false
    # 钉钉通知
    dingtalk:
      enabled: false
    # 企业微信通知
    wechat:
      enabled: false
  
  # API限流配置
  rate-limit:
    enabled: true
    # 每分钟请求次数
    requests-per-minute: 1000
    # 每小时请求次数
    requests-per-hour: 10000
  
  # 缓存配置
  cache:
    # 缓存类型：redis, caffeine
    type: redis
    # 默认过期时间（秒）
    default-ttl: 3600
    # 缓存前缀
    key-prefix: "kingdee:integration:"

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
  datasource:
    url: *************************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  
  data:
    redis:
      host: localhost
      port: 6379
      password: 
      database: 0
  
  mail:
    host: smtp.qq.com
    port: 587
    username: <EMAIL>
    password: your-email-password

# 开发环境日志级别
logging:
  level:
    com.kingdee.integration: DEBUG
    org.springframework.web: DEBUG

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  
  datasource:
    url: ***********************************************************
    username: postgres
    password: postgres
    driver-class-name: org.postgresql.Driver
  
  data:
    redis:
      host: test-redis
      port: 6379
      password: 
      database: 0

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:kingdee_integration_platform}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:postgres}
    driver-class-name: org.postgresql.Driver
  
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:0}
  
  mail:
    host: ${MAIL_HOST:smtp.qq.com}
    port: ${MAIL_PORT:587}
    username: ${MAIL_USERNAME:}
    password: ${MAIL_PASSWORD:}

# 生产环境日志级别
logging:
  level:
    root: WARN
    com.kingdee.integration: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN