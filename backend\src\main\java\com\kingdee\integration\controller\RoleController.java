package com.kingdee.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.entity.Role;
import com.kingdee.integration.service.RoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 角色管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/roles")
@RequiredArgsConstructor
@Validated
@Tag(name = "角色管理", description = "角色管理相关接口")
public class RoleController {

    private final RoleService roleService;

    /**
     * 分页查询角色列表
     */
    @GetMapping
    @Operation(summary = "分页查询角色列表", description = "根据条件分页查询角色列表")
    @PreAuthorize("hasPermission('role:list')")
    public Result<IPage<Role>> pageRoles(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "角色编码") @RequestParam(required = false) String roleCode,
            @Parameter(description = "角色名称") @RequestParam(required = false) String roleName,
            @Parameter(description = "角色类型") @RequestParam(required = false) String roleType,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<Role> page = new Page<>(current, size);
        IPage<Role> result = roleService.pageRoles(page, roleCode, roleName, roleType, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询角色详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询角色详情", description = "根据角色ID查询角色详情")
    @PreAuthorize("hasPermission('role:detail')")
    public Result<Role> getRoleById(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        Role role = roleService.getById(id);
        return Result.success(role);
    }

    /**
     * 根据角色编码查询角色
     */
    @GetMapping("/by-code/{roleCode}")
    @Operation(summary = "根据编码查询角色", description = "根据角色编码查询角色")
    @PreAuthorize("hasPermission('role:detail')")
    public Result<Role> getRoleByCode(
            @Parameter(description = "角色编码", required = true) @PathVariable @NotEmpty String roleCode) {
        
        Role role = roleService.getRoleByCode(roleCode);
        return Result.success(role);
    }

    /**
     * 创建角色
     */
    @PostMapping
    @Operation(summary = "创建角色", description = "创建新角色")
    @PreAuthorize("hasPermission('role:create')")
    public Result<Role> createRole(
            @Parameter(description = "角色信息", required = true) @RequestBody @Valid Role role) {
        
        Role createdRole = roleService.createRole(role);
        return Result.success(createdRole);
    }

    /**
     * 更新角色
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新角色", description = "更新角色信息")
    @PreAuthorize("hasPermission('role:update')")
    public Result<Role> updateRole(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "角色信息", required = true) @RequestBody @Valid Role role) {
        
        role.setId(id);
        Role updatedRole = roleService.updateRole(role);
        return Result.success(updatedRole);
    }

    /**
     * 删除角色
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除角色", description = "根据角色ID删除角色")
    @PreAuthorize("hasPermission('role:delete')")
    public Result<Void> deleteRole(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean deleted = roleService.deleteRole(id);
        return deleted ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量删除角色
     */
    @DeleteMapping
    @Operation(summary = "批量删除角色", description = "根据角色ID列表批量删除角色")
    @PreAuthorize("hasPermission('role:delete')")
    public Result<Void> deleteRoles(
            @Parameter(description = "角色ID列表", required = true) @RequestBody @NotEmpty List<Long> roleIds) {
        
        boolean deleted = roleService.deleteRoles(roleIds);
        return deleted ? Result.success() : Result.error("批量删除失败");
    }

    /**
     * 启用角色
     */
    @PutMapping("/{id}/enable")
    @Operation(summary = "启用角色", description = "启用指定角色")
    @PreAuthorize("hasPermission('role:update')")
    public Result<Void> enableRole(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean enabled = roleService.enableRole(id);
        return enabled ? Result.success() : Result.error("启用失败");
    }

    /**
     * 禁用角色
     */
    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用角色", description = "禁用指定角色")
    @PreAuthorize("hasPermission('role:update')")
    public Result<Void> disableRole(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean disabled = roleService.disableRole(id);
        return disabled ? Result.success() : Result.error("禁用失败");
    }

    /**
     * 分配角色权限
     */
    @PutMapping("/{id}/permissions")
    @Operation(summary = "分配角色权限", description = "为角色分配权限")
    @PreAuthorize("hasPermission('role:assign-permission')")
    public Result<Void> assignPermissions(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "权限ID列表", required = true) @RequestBody @NotEmpty List<Long> permissionIds) {
        
        boolean assigned = roleService.assignPermissions(id, permissionIds);
        return assigned ? Result.success() : Result.error("权限分配失败");
    }

    /**
     * 移除角色权限
     */
    @DeleteMapping("/{id}/permissions")
    @Operation(summary = "移除角色权限", description = "移除角色的指定权限")
    @PreAuthorize("hasPermission('role:remove-permission')")
    public Result<Void> removePermissions(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "权限ID列表", required = true) @RequestBody @NotEmpty List<Long> permissionIds) {
        
        boolean removed = roleService.removePermissions(id, permissionIds);
        return removed ? Result.success() : Result.error("权限移除失败");
    }

    /**
     * 获取角色权限列表
     */
    @GetMapping("/{id}/permissions")
    @Operation(summary = "获取角色权限", description = "获取角色的权限列表")
    @PreAuthorize("hasPermission('role:detail')")
    public Result<List<String>> getRolePermissions(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        List<String> permissions = roleService.getRolePermissions(id);
        return Result.success(permissions);
    }

    /**
     * 获取所有启用的角色
     */
    @GetMapping("/enabled")
    @Operation(summary = "获取启用角色", description = "获取所有启用状态的角色")
    @PreAuthorize("hasPermission('role:list')")
    public Result<List<Role>> getEnabledRoles() {
        
        List<Role> roles = roleService.getEnabledRoles();
        return Result.success(roles);
    }

    /**
     * 根据用户ID查询角色列表
     */
    @GetMapping("/by-user/{userId}")
    @Operation(summary = "根据用户查询角色", description = "根据用户ID查询角色列表")
    @PreAuthorize("hasPermission('role:list')")
    public Result<List<Role>> getRolesByUserId(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long userId) {
        
        List<Role> roles = roleService.getRolesByUserId(userId);
        return Result.success(roles);
    }

    /**
     * 根据角色类型查询角色列表
     */
    @GetMapping("/by-type/{roleType}")
    @Operation(summary = "根据类型查询角色", description = "根据角色类型查询角色列表")
    @PreAuthorize("hasPermission('role:list')")
    public Result<List<Role>> getRolesByType(
            @Parameter(description = "角色类型", required = true) @PathVariable @NotEmpty String roleType) {
        
        List<Role> roles = roleService.getRolesByType(roleType);
        return Result.success(roles);
    }

    /**
     * 检查角色编码是否存在
     */
    @GetMapping("/check-code")
    @Operation(summary = "检查角色编码", description = "检查角色编码是否已存在")
    public Result<Boolean> checkRoleCode(
            @Parameter(description = "角色编码", required = true) @RequestParam @NotEmpty String roleCode,
            @Parameter(description = "排除的角色ID") @RequestParam(required = false) Long excludeRoleId) {
        
        boolean exists = roleService.existsByRoleCode(roleCode, excludeRoleId);
        return Result.success(exists);
    }

    /**
     * 检查角色名称是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查角色名称", description = "检查角色名称是否已存在")
    public Result<Boolean> checkRoleName(
            @Parameter(description = "角色名称", required = true) @RequestParam @NotEmpty String roleName,
            @Parameter(description = "排除的角色ID") @RequestParam(required = false) Long excludeRoleId) {
        
        boolean exists = roleService.existsByRoleName(roleName, excludeRoleId);
        return Result.success(exists);
    }

    /**
     * 统计角色数量
     */
    @GetMapping("/count")
    @Operation(summary = "统计角色数量", description = "根据条件统计角色数量")
    @PreAuthorize("hasPermission('role:list')")
    public Result<Long> countRoles(
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "角色类型") @RequestParam(required = false) String roleType) {
        
        Long count = roleService.countRoles(status, roleType);
        return Result.success(count);
    }

    /**
     * 复制角色
     */
    @PostMapping("/{id}/copy")
    @Operation(summary = "复制角色", description = "复制现有角色创建新角色")
    @PreAuthorize("hasPermission('role:create')")
    public Result<Role> copyRole(
            @Parameter(description = "源角色ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "新角色编码", required = true) @RequestParam @NotEmpty String newRoleCode,
            @Parameter(description = "新角色名称", required = true) @RequestParam @NotEmpty String newRoleName,
            @Parameter(description = "是否复制权限") @RequestParam(defaultValue = "true") boolean copyPermissions) {
        
        Role copiedRole = roleService.copyRole(id, newRoleCode, newRoleName, copyPermissions);
        return Result.success(copiedRole);
    }

    /**
     * 导入角色
     */
    @PostMapping("/import")
    @Operation(summary = "导入角色", description = "批量导入角色")
    @PreAuthorize("hasPermission('role:import')")
    public Result<RoleService.ImportResult> importRoles(
            @Parameter(description = "角色列表", required = true) @RequestBody @NotEmpty List<Role> roles,
            @Parameter(description = "是否更新已存在的角色") @RequestParam(defaultValue = "false") boolean updateExisting) {
        
        RoleService.ImportResult result = roleService.importRoles(roles, updateExisting);
        return Result.success(result);
    }

    /**
     * 导出角色
     */
    @PostMapping("/export")
    @Operation(summary = "导出角色", description = "导出角色数据")
    @PreAuthorize("hasPermission('role:export')")
    public Result<List<Role>> exportRoles(
            @Parameter(description = "角色ID列表（为空则导出所有）") @RequestBody(required = false) List<Long> roleIds) {
        
        List<Role> roles = roleService.exportRoles(roleIds);
        return Result.success(roles);
    }

    /**
     * 同步角色缓存
     */
    @PostMapping("/sync-cache")
    @Operation(summary = "同步角色缓存", description = "同步角色相关缓存")
    @PreAuthorize("hasPermission('role:sync-cache')")
    public Result<Void> syncRoleCache() {
        
        roleService.syncRoleCache();
        return Result.success();
    }

    /**
     * 获取角色层级结构
     */
    @GetMapping("/hierarchy")
    @Operation(summary = "获取角色层级", description = "获取角色的层级结构")
    @PreAuthorize("hasPermission('role:list')")
    public Result<List<Role>> getRoleHierarchy() {
        
        List<Role> hierarchy = roleService.getRoleHierarchy();
        return Result.success(hierarchy);
    }

    /**
     * 获取角色详情（包含权限信息）
     */
    @GetMapping("/{id}/detail")
    @Operation(summary = "获取角色详情", description = "获取角色详情及其权限信息")
    @PreAuthorize("hasPermission('role:detail')")
    public Result<Role> getRoleDetail(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long id) {
        
        Role roleDetail = roleService.getRoleDetail(id);
        return Result.success(roleDetail);
    }
}