package com.kingdee.integration;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 金蝶云星辰业务扩展与集成平台主启动类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@SpringBootApplication
@EnableTransactionManagement
@EnableAsync
@EnableScheduling
@MapperScan("com.kingdee.integration.**.mapper")
public class KingdeeIntegrationPlatformApplication {

    public static void main(String[] args) {
        SpringApplication.run(KingdeeIntegrationPlatformApplication.class, args);
        System.out.println("");
        System.out.println("  _____ _   _ _______ ______ _____ _____            _______ _____ ____  _   _ ");
        System.out.println(" |_   _| \ | |__   __|  ____/ ____|  __ \     /\   |__   __|_   _/ __ \| \ | |");
        System.out.println("   | | |  \| |  | |  | |__ | |  __| |__) |   /  \     | |    | || |  | |  \| |");
        System.out.println("   | | | . ` |  | |  |  __|| | |_ |  _  /   / /\ \    | |    | || |  | | . ` |");
        System.out.println("  _| |_| |\  |  | |  | |___| |__| | | \ \  / ____ \   | |   _| || |__| | |\  |");
        System.out.println(" |_____|_| \_|  |_|  |______\_____|_|  \_\/_/    \_\  |_|  |_____\____/|_| \_|");
        System.out.println("");
        System.out.println(":: 金蝶云星辰业务扩展与集成平台 :: (v1.0.0)");
        System.out.println("");
    }
}