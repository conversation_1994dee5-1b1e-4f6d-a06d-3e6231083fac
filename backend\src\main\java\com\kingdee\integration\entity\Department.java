package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门实体类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("sys_department")
@Schema(name = "Department", description = "部门信息")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门ID")
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    @Schema(description = "部门编码", required = true)
    @NotBlank(message = "部门编码不能为空")
    @Size(max = 50, message = "部门编码长度不能超过50个字符")
    @TableField("dept_code")
    private String deptCode;

    @Schema(description = "部门名称", required = true)
    @NotBlank(message = "部门名称不能为空")
    @Size(max = 100, message = "部门名称长度不能超过100个字符")
    @TableField("dept_name")
    private String deptName;

    @Schema(description = "部门简称")
    @Size(max = 50, message = "部门简称长度不能超过50个字符")
    @TableField("dept_short_name")
    private String deptShortName;

    @Schema(description = "父部门ID")
    @TableField("parent_id")
    private Long parentId;

    @Schema(description = "部门层级")
    @TableField("dept_level")
    private Integer deptLevel;

    @Schema(description = "部门路径")
    @TableField("dept_path")
    private String deptPath;

    @Schema(description = "部门类型")
    @Size(max = 20, message = "部门类型长度不能超过20个字符")
    @TableField("dept_type")
    private String deptType;

    @Schema(description = "负责人ID")
    @TableField("manager_id")
    private Long managerId;

    @Schema(description = "负责人姓名")
    @Size(max = 50, message = "负责人姓名长度不能超过50个字符")
    @TableField("manager_name")
    private String managerName;

    @Schema(description = "联系电话")
    @Size(max = 20, message = "联系电话长度不能超过20个字符")
    @TableField("phone")
    private String phone;

    @Schema(description = "邮箱")
    @Size(max = 100, message = "邮箱长度不能超过100个字符")
    @TableField("email")
    private String email;

    @Schema(description = "传真")
    @Size(max = 20, message = "传真长度不能超过20个字符")
    @TableField("fax")
    private String fax;

    @Schema(description = "地址")
    @Size(max = 200, message = "地址长度不能超过200个字符")
    @TableField("address")
    private String address;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "状态：0-禁用，1-启用")
    @NotNull(message = "状态不能为空")
    @TableField("status")
    private Integer status;

    @Schema(description = "描述")
    @Size(max = 500, message = "描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    @Schema(description = "扩展属性（JSON格式）")
    @TableField("extra_attrs")
    private String extraAttrs;

    @Schema(description = "创建人ID")
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private Long createBy;

    @Schema(description = "创建人姓名")
    @TableField(value = "create_by_name", fill = FieldFill.INSERT)
    private String createByName;

    @Schema(description = "创建时间")
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @Schema(description = "更新人ID")
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @Schema(description = "更新人姓名")
    @TableField(value = "update_by_name", fill = FieldFill.INSERT_UPDATE)
    private String updateByName;

    @Schema(description = "更新时间")
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    @Schema(description = "是否删除：0-未删除，1-已删除")
    @TableField("is_deleted")
    @TableLogic
    private Integer isDeleted;

    @Schema(description = "版本号")
    @TableField("version")
    @Version
    private Integer version;

    // 非数据库字段
    @Schema(description = "父部门名称")
    @TableField(exist = false)
    private String parentName;

    @Schema(description = "子部门列表")
    @TableField(exist = false)
    private List<Department> children;

    @Schema(description = "部门用户数量")
    @TableField(exist = false)
    private Long userCount;

    @Schema(description = "是否有子部门")
    @TableField(exist = false)
    private Boolean hasChildren;

    @Schema(description = "部门全路径名称")
    @TableField(exist = false)
    private String fullPath;

    @Schema(description = "部门层级名称")
    @TableField(exist = false)
    private String levelName;

    /**
     * 获取部门全名（包含层级）
     */
    public String getFullName() {
        if (parentName != null && !parentName.isEmpty()) {
            return parentName + " > " + deptName;
        }
        return deptName;
    }

    /**
     * 是否为根部门
     */
    public boolean isRoot() {
        return parentId == null || parentId == 0;
    }

    /**
     * 是否为叶子部门
     */
    public boolean isLeaf() {
        return hasChildren == null || !hasChildren;
    }

    /**
     * 获取部门层级显示名称
     */
    public String getLevelDisplayName() {
        if (deptLevel == null) {
            return deptName;
        }
        StringBuilder prefix = new StringBuilder();
        for (int i = 1; i < deptLevel; i++) {
            prefix.append("　");
        }
        prefix.append("├─ ");
        return prefix + deptName;
    }
}