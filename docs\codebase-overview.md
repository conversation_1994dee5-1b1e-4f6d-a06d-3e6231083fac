# Link-XingChen 项目代码库概览

本文档提供了 Link-XingChen 项目的完整代码库结构和功能概述，帮助开发者快速了解项目架构和继续开发工作。

## 项目概述

**项目名称**: 金蝶云星辰业务扩展与集成平台 (Link-XingChen)  
**项目类型**: Spring Boot 企业级集成平台  
**主要功能**: 与金蝶云星辰系统的双向数据同步和业务流程集成  

## 技术栈

### 后端技术栈
- **框架**: Spring Boot 3.2.1
- **Java 版本**: Java 17
- **安全框架**: Spring Security + JWT
- **数据访问**: Spring Data JPA + MyBatis Plus 3.5.5
- **数据库**: PostgreSQL (主数据库) + Redis (缓存)
- **构建工具**: Maven
- **API 文档**: Knife4j (Swagger)
- **其他组件**: 
  - Druid (数据库连接池)
  - Redisson (Redis 客户端)
  - FastJSON2 (JSON 处理)
  - Hutool (工具类库)
  - MinIO (对象存储)
  - EasyExcel (Excel 处理)

### 前端技术栈 (规划中)
- **框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **UI 组件**: Element Plus
- **状态管理**: Pinia

## 项目结构

```
Link-XingChen/
├── backend/                          # 后端 Spring Boot 项目
│   ├── src/main/java/com/kingdee/integration/
│   │   ├── Application.java          # 主启动类
│   │   ├── common/                   # 公共组件
│   │   │   ├── entity/              # 基础实体类
│   │   │   ├── exception/           # 异常定义
│   │   │   ├── page/                # 分页组件
│   │   │   └── result/              # 统一响应格式
│   │   ├── config/                  # 配置类
│   │   │   ├── SecurityConfig.java  # 安全配置
│   │   │   └── MyBatisPlusConfig.java # 数据库配置
│   │   ├── controller/              # 控制器层
│   │   │   ├── AuthController.java  # 认证控制器
│   │   │   ├── UserController.java  # 用户管理
│   │   │   ├── RoleController.java  # 角色管理
│   │   │   └── DepartmentController.java # 部门管理
│   │   ├── dto/                     # 数据传输对象
│   │   │   └── request/             # 请求 DTO
│   │   ├── entity/                  # 实体类
│   │   │   ├── User.java           # 用户实体
│   │   │   ├── Role.java           # 角色实体
│   │   │   ├── Permission.java     # 权限实体
│   │   │   └── Department.java     # 部门实体
│   │   ├── exception/               # 异常处理
│   │   ├── mapper/                  # MyBatis 映射器
│   │   ├── security/                # 安全组件
│   │   │   ├── JwtAuthenticationFilter.java # JWT 过滤器
│   │   │   └── JwtAuthenticationEntryPoint.java # 认证入口点
│   │   ├── service/                 # 服务层
│   │   │   └── impl/               # 服务实现
│   │   └── util/                   # 工具类
│   │       ├── JwtUtil.java        # JWT 工具
│   │       ├── PasswordUtil.java   # 密码工具
│   │       └── RedisUtil.java      # Redis 工具
│   ├── src/main/resources/
│   │   ├── application.yml         # 应用配置
│   │   └── i18n/                   # 国际化资源
│   └── pom.xml                     # Maven 配置
├── lib/                            # SDK 库文件目录
│   └── README.md                   # SDK 说明文档
├── docs/                           # 项目文档
│   ├── codebase-overview.md        # 本文档
│   └── development/                # 开发文档
│       └── xingchen-authentication-guide.md # 认证开发指南
├── architecture.md                 # 系统架构设计
├── requirements.md                 # 需求文档 (包含开发指南)
└── database-schema.sql            # 数据库结构
```

## 核心功能模块

### 1. 用户认证与权限管理
- **位置**: `backend/src/main/java/com/kingdee/integration/security/`
- **功能**: JWT 认证、用户登录、权限控制
- **主要类**:
  - `JwtAuthenticationFilter`: JWT 认证过滤器
  - `JwtUtil`: JWT 令牌工具类
  - `SecurityConfig`: Spring Security 配置

### 2. 用户管理系统
- **位置**: `backend/src/main/java/com/kingdee/integration/controller/`
- **功能**: 用户、角色、部门管理
- **主要类**:
  - `UserController`: 用户管理接口
  - `RoleController`: 角色管理接口
  - `DepartmentController`: 部门管理接口

### 3. 数据访问层
- **位置**: `backend/src/main/java/com/kingdee/integration/mapper/`
- **技术**: MyBatis Plus + Spring Data JPA
- **功能**: 数据库操作、实体映射

### 4. 公共组件
- **位置**: `backend/src/main/java/com/kingdee/integration/common/`
- **功能**: 统一响应格式、异常处理、分页组件

## 数据库设计

### 核心表结构
- **sys_users**: 系统用户表
- **sys_roles**: 系统角色表  
- **sys_permissions**: 系统权限表
- **sys_user_roles**: 用户角色关联表
- **sys_role_permissions**: 角色权限关联表
- **biz_organizations**: 组织架构表
- **biz_materials**: 物料主数据表
- **biz_customers**: 客户主数据表
- **biz_suppliers**: 供应商主数据表

详细的数据库结构请参考 `database-schema.sql` 文件。

## 配置说明

### 应用配置 (application.yml)
```yaml
# 主要配置项
spring:
  application:
    name: kingdee-integration-platform
  profiles:
    active: dev

# 自定义配置
app:
  jwt:
    secret: kingdee-integration-platform-jwt-secret-key-2024
    expiration: 86400  # 24小时
  security:
    password-salt: kingdee-integration-platform-salt
    max-login-attempts: 5
```

### 环境配置
- **开发环境**: PostgreSQL + Redis (本地)
- **测试环境**: 容器化部署
- **生产环境**: 集群部署 + 负载均衡

## API 接口设计

### 认证接口
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/refresh` - 刷新令牌
- `POST /api/auth/logout` - 用户登出

### 用户管理接口
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建用户
- `PUT /api/users/{id}` - 更新用户
- `DELETE /api/users/{id}` - 删除用户

### 星辰系统接口 (规划中)
- `GET /api/xingchen/**` - 星辰系统相关接口

## 安全设计

### 认证机制
- **JWT 令牌认证**: 无状态认证
- **角色权限控制**: 基于 RBAC 模型
- **接口权限**: 细粒度权限控制

### 安全配置
- **密码加密**: BCrypt 加密
- **敏感数据**: 配置文件加密存储
- **HTTPS**: 生产环境强制 HTTPS
- **CORS**: 跨域请求控制

## 开发环境搭建

### 1. 环境要求
- Java 17+
- Maven 3.6+
- PostgreSQL 14+
- Redis 7+
- IDE: IntelliJ IDEA 或 Eclipse

### 2. 启动步骤
```bash
# 1. 克隆项目
git clone <repository-url>

# 2. 进入后端目录
cd backend

# 3. 安装依赖
mvn clean install

# 4. 启动应用
mvn spring-boot:run
```

### 3. 访问地址
- **应用地址**: http://localhost:8080
- **API 文档**: http://localhost:8080/swagger-ui.html
- **健康检查**: http://localhost:8080/actuator/health

## 集成开发指南

### 1. XingChen SDK 集成
- **SDK 位置**: `lib/` 目录
- **配置文件**: 参考 `lib/README.md`
- **认证示例**: 参考 `docs/development/xingchen-authentication-guide.md`

### 2. 添加新功能模块
1. 在 `entity/` 目录创建实体类
2. 在 `mapper/` 目录创建数据访问接口
3. 在 `service/` 目录创建业务逻辑
4. 在 `controller/` 目录创建 REST 接口
5. 更新数据库结构 (`database-schema.sql`)

### 3. 测试指南
```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify

# 生成测试报告
mvn jacoco:report
```

## 部署指南

### 1. 开发环境
- 使用 `dev` profile
- 本地数据库连接
- 详细日志输出

### 2. 生产环境
- 使用 `prod` profile
- 外部配置文件
- 容器化部署

### 3. 监控运维
- **健康检查**: Spring Boot Actuator
- **指标监控**: Prometheus + Grafana
- **日志管理**: ELK Stack

## 后续开发计划

### 短期目标 (1-2 个月)
1. 完成 XingChen SDK 集成
2. 实现主数据同步功能
3. 开发业务单据管理
4. 完善 API 文档

### 中期目标 (3-6 个月)
1. 开发前端管理界面
2. 实现工作流引擎
3. 集成协同办公工具
4. 性能优化和安全加固

### 长期目标 (6-12 个月)
1. 微服务架构改造
2. 多租户支持
3. 插件机制开发
4. 国际化支持

## 技术支持

### 文档资源
- **架构设计**: `architecture.md`
- **需求文档**: `requirements.md`
- **开发指南**: `docs/development/`

### 联系方式
- **项目负责人**: [待补充]
- **技术支持**: [待补充]
- **问题反馈**: [待补充]

---

**最后更新**: 2024-01-15  
**文档版本**: v1.0.0
