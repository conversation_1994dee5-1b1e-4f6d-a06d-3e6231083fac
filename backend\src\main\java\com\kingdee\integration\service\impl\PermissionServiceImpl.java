package com.kingdee.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kingdee.integration.entity.Permission;
import com.kingdee.integration.mapper.PermissionMapper;
import com.kingdee.integration.service.PermissionService;
import com.kingdee.integration.exception.BusinessException;
import com.kingdee.integration.enums.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限服务实现类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl extends ServiceImpl<PermissionMapper, Permission> implements PermissionService {

    @Override
    @Cacheable(value = "permission", key = "#permissionCode")
    public Permission getByPermissionCode(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            return null;
        }
        return baseMapper.selectByPermissionCode(permissionCode);
    }

    @Override
    @Cacheable(value = "permission", key = "#permissionName")
    public Permission getByPermissionName(String permissionName) {
        if (!StringUtils.hasText(permissionName)) {
            return null;
        }
        return baseMapper.selectByPermissionName(permissionName);
    }

    @Override
    public IPage<Permission> pagePermissions(Page<Permission> page, String permissionName, 
                                           String permissionCode, String permissionType, 
                                           Integer status, Long parentId) {
        return baseMapper.selectPermissionPage(page, permissionName, permissionCode, 
                                             permissionType, status, parentId);
    }

    @Override
    @Cacheable(value = "permission", key = "'tree'")
    public List<Permission> getPermissionTree() {
        return baseMapper.selectPermissionTree();
    }

    @Override
    @Cacheable(value = "permission", key = "'enabled'")
    public List<Permission> getEnabledPermissions() {
        return baseMapper.selectEnabledPermissions();
    }

    @Override
    public List<Permission> getPermissionsByType(String permissionType) {
        if (!StringUtils.hasText(permissionType)) {
            return Collections.emptyList();
        }
        return baseMapper.selectPermissionsByType(permissionType);
    }

    @Override
    public List<Permission> getPermissionsByRoleId(Long roleId) {
        if (roleId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectPermissionsByRoleId(roleId);
    }

    @Override
    public List<Permission> getPermissionsByUserId(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectPermissionsByUserId(userId);
    }

    @Override
    public List<Permission> getMenuPermissions() {
        return baseMapper.selectMenuPermissions();
    }

    @Override
    public List<Permission> getButtonPermissions() {
        return baseMapper.selectButtonPermissions();
    }

    @Override
    public List<Permission> getApiPermissions() {
        return baseMapper.selectApiPermissions();
    }

    @Override
    public List<Permission> getChildPermissions(Long parentId) {
        if (parentId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectChildPermissions(parentId);
    }

    @Override
    public String getPermissionPath(Long permissionId) {
        if (permissionId == null) {
            return "";
        }
        return baseMapper.selectPermissionPath(permissionId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean createPermission(Permission permission) {
        // 验证权限编码唯一性
        if (isPermissionCodeExists(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }
        
        // 验证权限名称唯一性
        if (isPermissionNameExists(permission.getPermissionName())) {
            throw new BusinessException(ErrorCode.PERMISSION_NAME_EXISTS);
        }
        
        // 验证URL唯一性（如果是API权限）
        if ("API".equals(permission.getPermissionType()) && 
            StringUtils.hasText(permission.getUrl()) && 
            isUrlExists(permission.getUrl())) {
            throw new BusinessException(ErrorCode.PERMISSION_URL_EXISTS);
        }
        
        // 验证父权限是否存在
        if (permission.getParentId() != null && permission.getParentId() > 0) {
            Permission parent = getById(permission.getParentId());
            if (parent == null) {
                throw new BusinessException(ErrorCode.PARENT_PERMISSION_NOT_FOUND);
            }
        }
        
        permission.setCreateTime(LocalDateTime.now());
        permission.setUpdateTime(LocalDateTime.now());
        permission.setStatus(1); // 默认启用
        
        // 设置排序号
        if (permission.getSortOrder() == null) {
            Integer maxSort = getMaxSortOrder(permission.getParentId());
            permission.setSortOrder(maxSort + 1);
        }
        
        boolean result = save(permission);
        if (result) {
            // 更新权限路径
            updatePermissionPath(permission.getId());
            log.info("创建权限成功: {}", permission.getPermissionName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean updatePermission(Permission permission) {
        Permission existingPermission = getById(permission.getId());
        if (existingPermission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }
        
        // 如果权限编码发生变化，验证新编码的唯一性
        if (!existingPermission.getPermissionCode().equals(permission.getPermissionCode()) && 
            isPermissionCodeExists(permission.getPermissionCode())) {
            throw new BusinessException(ErrorCode.PERMISSION_CODE_EXISTS);
        }
        
        // 如果权限名称发生变化，验证新名称的唯一性
        if (!existingPermission.getPermissionName().equals(permission.getPermissionName()) && 
            isPermissionNameExists(permission.getPermissionName())) {
            throw new BusinessException(ErrorCode.PERMISSION_NAME_EXISTS);
        }
        
        // 如果URL发生变化，验证新URL的唯一性
        if ("API".equals(permission.getPermissionType()) && 
            StringUtils.hasText(permission.getUrl()) && 
            !Objects.equals(existingPermission.getUrl(), permission.getUrl()) && 
            isUrlExists(permission.getUrl())) {
            throw new BusinessException(ErrorCode.PERMISSION_URL_EXISTS);
        }
        
        // 验证父权限是否存在
        if (permission.getParentId() != null && permission.getParentId() > 0) {
            Permission parent = getById(permission.getParentId());
            if (parent == null) {
                throw new BusinessException(ErrorCode.PARENT_PERMISSION_NOT_FOUND);
            }
            
            // 防止设置自己为父权限
            if (permission.getParentId().equals(permission.getId())) {
                throw new BusinessException(ErrorCode.INVALID_PARENT_PERMISSION);
            }
        }
        
        permission.setUpdateTime(LocalDateTime.now());
        
        boolean result = updateById(permission);
        if (result) {
            // 如果父权限发生变化，更新权限路径
            if (!Objects.equals(existingPermission.getParentId(), permission.getParentId())) {
                updatePermissionPath(permission.getId());
                updateChildPermissionPaths(permission.getId());
            }
            log.info("更新权限成功: {}", permission.getPermissionName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean deletePermission(Long permissionId) {
        Permission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }
        
        // 检查是否有子权限
        List<Permission> children = getChildPermissions(permissionId);
        if (!children.isEmpty()) {
            throw new BusinessException(ErrorCode.PERMISSION_HAS_CHILDREN);
        }
        
        // 检查权限是否被角色使用
        if (isPermissionInUse(permissionId)) {
            throw new BusinessException(ErrorCode.PERMISSION_IN_USE);
        }
        
        boolean result = removeById(permissionId);
        if (result) {
            log.info("删除权限成功: {}", permission.getPermissionName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean deleteBatchPermissions(List<Long> permissionIds) {
        if (permissionIds == null || permissionIds.isEmpty()) {
            return false;
        }
        
        // 检查权限是否有子权限或被使用
        for (Long permissionId : permissionIds) {
            List<Permission> children = getChildPermissions(permissionId);
            if (!children.isEmpty()) {
                Permission permission = getById(permissionId);
                throw new BusinessException(ErrorCode.PERMISSION_HAS_CHILDREN, 
                    "权限 '" + (permission != null ? permission.getPermissionName() : permissionId) + "' 存在子权限，无法删除");
            }
            
            if (isPermissionInUse(permissionId)) {
                Permission permission = getById(permissionId);
                throw new BusinessException(ErrorCode.PERMISSION_IN_USE, 
                    "权限 '" + (permission != null ? permission.getPermissionName() : permissionId) + "' 正在被使用，无法删除");
            }
        }
        
        boolean result = removeByIds(permissionIds);
        if (result) {
            log.info("批量删除权限成功，数量: {}", permissionIds.size());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean enablePermission(Long permissionId) {
        return updatePermissionStatus(permissionId, 1);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean disablePermission(Long permissionId) {
        return updatePermissionStatus(permissionId, 0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean batchUpdateStatus(List<Long> permissionIds, Integer status) {
        if (permissionIds == null || permissionIds.isEmpty() || status == null) {
            return false;
        }
        
        boolean result = baseMapper.batchUpdateStatus(permissionIds, status) > 0;
        if (result) {
            String action = status == 1 ? "启用" : "禁用";
            log.info("批量{}权限成功，数量: {}", action, permissionIds.size());
        }
        return result;
    }

    @Override
    public boolean isPermissionCodeExists(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            return false;
        }
        return baseMapper.existsByPermissionCode(permissionCode);
    }

    @Override
    public boolean isPermissionNameExists(String permissionName) {
        if (!StringUtils.hasText(permissionName)) {
            return false;
        }
        return baseMapper.existsByPermissionName(permissionName);
    }

    @Override
    public boolean isUrlExists(String url) {
        if (!StringUtils.hasText(url)) {
            return false;
        }
        return baseMapper.existsByUrl(url);
    }

    @Override
    public Long countPermissions() {
        return baseMapper.countPermissions();
    }

    @Override
    public Long countPermissionsByType(String permissionType) {
        if (!StringUtils.hasText(permissionType)) {
            return 0L;
        }
        return baseMapper.countPermissionsByType(permissionType);
    }

    @Override
    public Long countPermissionsByStatus(Integer status) {
        if (status == null) {
            return 0L;
        }
        return baseMapper.countPermissionsByStatus(status);
    }

    @Override
    public Integer getMaxSortOrder(Long parentId) {
        Integer maxSort = baseMapper.selectMaxSortOrder(parentId);
        return maxSort != null ? maxSort : 0;
    }

    @Override
    public Permission getPermissionWithParent(Long permissionId) {
        if (permissionId == null) {
            return null;
        }
        return baseMapper.selectPermissionWithParent(permissionId);
    }

    @Override
    public boolean isPermissionInUse(Long permissionId) {
        if (permissionId == null) {
            return false;
        }
        return baseMapper.isPermissionInUse(permissionId);
    }

    @Override
    public Permission getByUrlAndMethod(String url, String method) {
        if (!StringUtils.hasText(url) || !StringUtils.hasText(method)) {
            return null;
        }
        return baseMapper.selectByUrlAndMethod(url, method);
    }

    @Override
    public List<Permission> getUserMenuTree(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectUserMenuTree(userId);
    }

    @Override
    public List<Permission> getUserButtonPermissions(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectUserButtonPermissions(userId);
    }

    @Override
    public List<Permission> getUserApiPermissions(Long userId) {
        if (userId == null) {
            return Collections.emptyList();
        }
        return baseMapper.selectUserApiPermissions(userId);
    }

    @Override
    public List<Permission> getPermissionsByRoleIds(List<Long> roleIds) {
        if (roleIds == null || roleIds.isEmpty()) {
            return Collections.emptyList();
        }
        return baseMapper.selectPermissionsByRoleIds(roleIds);
    }

    @Override
    public List<Permission> getSystemPermissions() {
        return baseMapper.selectSystemPermissions();
    }

    @Override
    public List<Permission> getBusinessPermissions() {
        return baseMapper.selectBusinessPermissions();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean updatePermissionPath(Long permissionId) {
        if (permissionId == null) {
            return false;
        }
        
        String path = buildPermissionPath(permissionId);
        boolean result = baseMapper.updatePermissionPath(permissionId, path) > 0;
        if (result) {
            log.debug("更新权限路径成功: {} -> {}", permissionId, path);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = "permission", allEntries = true)
    public boolean batchUpdateChildPermissionPaths(Long parentId) {
        if (parentId == null) {
            return false;
        }
        
        List<Permission> children = getChildPermissions(parentId);
        for (Permission child : children) {
            updatePermissionPath(child.getId());
            // 递归更新子权限的路径
            batchUpdateChildPermissionPaths(child.getId());
        }
        
        log.info("批量更新子权限路径成功，父权限ID: {}", parentId);
        return true;
    }

    /**
     * 更新权限状态
     */
    private boolean updatePermissionStatus(Long permissionId, Integer status) {
        Permission permission = getById(permissionId);
        if (permission == null) {
            throw new BusinessException(ErrorCode.PERMISSION_NOT_FOUND);
        }
        
        boolean result = baseMapper.updatePermissionStatus(permissionId, status) > 0;
        if (result) {
            String action = status == 1 ? "启用" : "禁用";
            log.info("{}权限成功: {}", action, permission.getPermissionName());
        }
        return result;
    }

    /**
     * 更新子权限路径
     */
    private void updateChildPermissionPaths(Long parentId) {
        List<Permission> children = getChildPermissions(parentId);
        for (Permission child : children) {
            updatePermissionPath(child.getId());
            updateChildPermissionPaths(child.getId());
        }
    }

    /**
     * 构建权限路径
     */
    private String buildPermissionPath(Long permissionId) {
        List<String> pathParts = new ArrayList<>();
        Permission current = getById(permissionId);
        
        while (current != null) {
            pathParts.add(0, current.getPermissionCode());
            if (current.getParentId() == null || current.getParentId() == 0) {
                break;
            }
            current = getById(current.getParentId());
        }
        
        return String.join(".", pathParts);
    }
}