<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kingdee.integration.mapper.UserMapper">

    <!-- 用户结果映射 -->
    <resultMap id="BaseResultMap" type="com.kingdee.integration.entity.User">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="password" property="password" jdbcType="VARCHAR"/>
        <result column="real_name" property="realName" jdbcType="VARCHAR"/>
        <result column="email" property="email" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="avatar" property="avatar" jdbcType="VARCHAR"/>
        <result column="gender" property="gender" jdbcType="INTEGER"/>
        <result column="birthday" property="birthday" jdbcType="TIMESTAMP"/>
        <result column="department_id" property="departmentId" jdbcType="BIGINT"/>
        <result column="position" property="position" jdbcType="VARCHAR"/>
        <result column="employee_no" property="employeeNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="locked" property="locked" jdbcType="INTEGER"/>
        <result column="password_expire_time" property="passwordExpireTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="TIMESTAMP"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="login_failure_count" property="loginFailureCount" jdbcType="INTEGER"/>
        <result column="lock_time" property="lockTime" jdbcType="TIMESTAMP"/>
        <result column="user_type" property="userType" jdbcType="INTEGER"/>
        <result column="language" property="language" jdbcType="VARCHAR"/>
        <result column="timezone" property="timezone" jdbcType="VARCHAR"/>
        <result column="theme" property="theme" jdbcType="VARCHAR"/>
        <result column="extra_data" property="extraData" jdbcType="VARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="created_by" property="createdBy" jdbcType="BIGINT"/>
        <result column="updated_by" property="updatedBy" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="VARCHAR"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 用户详情结果映射（包含部门信息） -->
    <resultMap id="UserDetailResultMap" type="com.kingdee.integration.entity.User" extends="BaseResultMap">
        <result column="department_name" property="departmentName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, username, password, real_name, email, phone, avatar, gender, birthday,
        department_id, position, employee_no, status, locked, password_expire_time,
        last_login_time, last_login_ip, login_failure_count, lock_time, user_type,
        language, timezone, theme, extra_data, created_at, updated_at, created_by,
        updated_by, deleted, version, tenant_id, remark
    </sql>

    <!-- 分页查询用户列表（带部门信息） -->
    <select id="selectUserPage" resultMap="UserDetailResultMap">
        SELECT 
            u.*,
            d.department_name
        FROM sys_user u
        LEFT JOIN sys_department d ON u.department_id = d.id AND d.deleted = 0
        WHERE u.deleted = 0
        <if test="username != null and username != ''">
            AND u.username LIKE CONCAT('%', #{username}, '%')
        </if>
        <if test="realName != null and realName != ''">
            AND u.real_name LIKE CONCAT('%', #{realName}, '%')
        </if>
        <if test="email != null and email != ''">
            AND u.email LIKE CONCAT('%', #{email}, '%')
        </if>
        <if test="departmentId != null">
            AND u.department_id = #{departmentId}
        </if>
        <if test="status != null">
            AND u.status = #{status}
        </if>
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据角色ID查询用户列表 -->
    <select id="selectUsersByRoleId" resultMap="UserDetailResultMap">
        SELECT 
            u.*,
            d.department_name
        FROM sys_user u
        LEFT JOIN sys_department d ON u.department_id = d.id AND d.deleted = 0
        INNER JOIN sys_user_role ur ON u.id = ur.user_id AND ur.deleted = 0
        WHERE u.deleted = 0 
          AND ur.role_id = #{roleId}
          AND ur.status = 1
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据部门ID查询用户列表 -->
    <select id="selectUsersByDepartmentId" resultMap="UserDetailResultMap">
        SELECT 
            u.*,
            d.department_name
        FROM sys_user u
        LEFT JOIN sys_department d ON u.department_id = d.id AND d.deleted = 0
        WHERE u.deleted = 0
        <choose>
            <when test="includeSubDept != null and includeSubDept == true">
                AND (u.department_id = #{departmentId} 
                     OR u.department_id IN (
                         SELECT id FROM sys_department 
                         WHERE deleted = 0 
                           AND (parent_id = #{departmentId} 
                                OR tree_path LIKE CONCAT('%/', #{departmentId}, '/%'))
                     ))
            </when>
            <otherwise>
                AND u.department_id = #{departmentId}
            </otherwise>
        </choose>
        ORDER BY u.created_at DESC
    </select>

    <!-- 根据权限编码查询用户列表 -->
    <select id="selectUsersByPermissionCode" resultMap="UserDetailResultMap">
        SELECT DISTINCT
            u.*,
            d.department_name
        FROM sys_user u
        LEFT JOIN sys_department d ON u.department_id = d.id AND d.deleted = 0
        INNER JOIN sys_user_role ur ON u.id = ur.user_id AND ur.deleted = 0 AND ur.status = 1
        INNER JOIN sys_role_permission rp ON ur.role_id = rp.role_id AND rp.deleted = 0 AND rp.status = 1
        INNER JOIN sys_permission p ON rp.permission_id = p.id AND p.deleted = 0 AND p.status = 1
        WHERE u.deleted = 0 
          AND u.status = 1
          AND p.permission_code = #{permissionCode}
        ORDER BY u.created_at DESC
    </select>

</mapper>