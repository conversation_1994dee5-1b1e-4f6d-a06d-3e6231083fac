package com.kingdee.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.entity.User;
import com.kingdee.integration.service.UserService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 用户管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/users")
@RequiredArgsConstructor
@Validated
@Tag(name = "用户管理", description = "用户管理相关接口")
public class UserController {

    private final UserService userService;

    /**
     * 分页查询用户列表
     */
    @GetMapping
    @Operation(summary = "分页查询用户列表", description = "根据条件分页查询用户列表")
    @PreAuthorize("hasPermission('user:list')")
    public Result<IPage<User>> pageUsers(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "用户名") @RequestParam(required = false) String username,
            @Parameter(description = "真实姓名") @RequestParam(required = false) String realName,
            @Parameter(description = "邮箱") @RequestParam(required = false) String email,
            @Parameter(description = "部门ID") @RequestParam(required = false) Long departmentId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<User> page = new Page<>(current, size);
        IPage<User> result = userService.pageUsers(page, username, realName, email, departmentId, status);
        return Result.success(result);
    }

    /**
     * 根据ID查询用户详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询用户详情", description = "根据用户ID查询用户详情")
    @PreAuthorize("hasPermission('user:detail')")
    public Result<User> getUserById(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        User user = userService.getById(id);
        return Result.success(user);
    }

    /**
     * 创建用户
     */
    @PostMapping
    @Operation(summary = "创建用户", description = "创建新用户")
    @PreAuthorize("hasPermission('user:create')")
    public Result<User> createUser(
            @Parameter(description = "用户信息", required = true) @RequestBody @Valid User user) {
        
        User createdUser = userService.createUser(user);
        return Result.success(createdUser);
    }

    /**
     * 更新用户
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新用户", description = "更新用户信息")
    @PreAuthorize("hasPermission('user:update')")
    public Result<User> updateUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "用户信息", required = true) @RequestBody @Valid User user) {
        
        user.setId(id);
        User updatedUser = userService.updateUser(user);
        return Result.success(updatedUser);
    }

    /**
     * 删除用户
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @PreAuthorize("hasPermission('user:delete')")
    public Result<Void> deleteUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean deleted = userService.deleteUser(id);
        return deleted ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量删除用户
     */
    @DeleteMapping
    @Operation(summary = "批量删除用户", description = "根据用户ID列表批量删除用户")
    @PreAuthorize("hasPermission('user:delete')")
    public Result<Void> deleteUsers(
            @Parameter(description = "用户ID列表", required = true) @RequestBody @NotEmpty List<Long> userIds) {
        
        boolean deleted = userService.deleteUsers(userIds);
        return deleted ? Result.success() : Result.error("批量删除失败");
    }

    /**
     * 启用用户
     */
    @PutMapping("/{id}/enable")
    @Operation(summary = "启用用户", description = "启用指定用户")
    @PreAuthorize("hasPermission('user:update')")
    public Result<Void> enableUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean enabled = userService.enableUser(id);
        return enabled ? Result.success() : Result.error("启用失败");
    }

    /**
     * 禁用用户
     */
    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用用户", description = "禁用指定用户")
    @PreAuthorize("hasPermission('user:update')")
    public Result<Void> disableUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean disabled = userService.disableUser(id);
        return disabled ? Result.success() : Result.error("禁用失败");
    }

    /**
     * 锁定用户
     */
    @PutMapping("/{id}/lock")
    @Operation(summary = "锁定用户", description = "锁定指定用户")
    @PreAuthorize("hasPermission('user:update')")
    public Result<Void> lockUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean locked = userService.lockUser(id);
        return locked ? Result.success() : Result.error("锁定失败");
    }

    /**
     * 解锁用户
     */
    @PutMapping("/{id}/unlock")
    @Operation(summary = "解锁用户", description = "解锁指定用户")
    @PreAuthorize("hasPermission('user:update')")
    public Result<Void> unlockUser(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean unlocked = userService.unlockUser(id);
        return unlocked ? Result.success() : Result.error("解锁失败");
    }

    /**
     * 重置用户密码
     */
    @PutMapping("/{id}/reset-password")
    @Operation(summary = "重置用户密码", description = "重置指定用户的密码")
    @PreAuthorize("hasPermission('user:reset-password')")
    public Result<Void> resetPassword(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "新密码", required = true) @RequestParam @NotEmpty String newPassword) {
        
        boolean reset = userService.resetPassword(id, newPassword);
        return reset ? Result.success() : Result.error("密码重置失败");
    }

    /**
     * 修改用户密码
     */
    @PutMapping("/{id}/change-password")
    @Operation(summary = "修改用户密码", description = "修改指定用户的密码")
    @PreAuthorize("hasPermission('user:change-password') or #id == authentication.principal.id")
    public Result<Void> changePassword(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "旧密码", required = true) @RequestParam @NotEmpty String oldPassword,
            @Parameter(description = "新密码", required = true) @RequestParam @NotEmpty String newPassword) {
        
        boolean changed = userService.changePassword(id, oldPassword, newPassword);
        return changed ? Result.success() : Result.error("密码修改失败");
    }

    /**
     * 分配用户角色
     */
    @PutMapping("/{id}/roles")
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @PreAuthorize("hasPermission('user:assign-role')")
    public Result<Void> assignRoles(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "角色ID列表", required = true) @RequestBody @NotEmpty List<Long> roleIds) {
        
        boolean assigned = userService.assignRoles(id, roleIds);
        return assigned ? Result.success() : Result.error("角色分配失败");
    }

    /**
     * 移除用户角色
     */
    @DeleteMapping("/{id}/roles")
    @Operation(summary = "移除用户角色", description = "移除用户的指定角色")
    @PreAuthorize("hasPermission('user:remove-role')")
    public Result<Void> removeRoles(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "角色ID列表", required = true) @RequestBody @NotEmpty List<Long> roleIds) {
        
        boolean removed = userService.removeRoles(id, roleIds);
        return removed ? Result.success() : Result.error("角色移除失败");
    }

    /**
     * 获取用户角色列表
     */
    @GetMapping("/{id}/roles")
    @Operation(summary = "获取用户角色", description = "获取用户的角色列表")
    @PreAuthorize("hasPermission('user:detail') or #id == authentication.principal.id")
    public Result<List<String>> getUserRoles(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        List<String> roles = userService.getUserRoles(id);
        return Result.success(roles);
    }

    /**
     * 获取用户权限列表
     */
    @GetMapping("/{id}/permissions")
    @Operation(summary = "获取用户权限", description = "获取用户的权限列表")
    @PreAuthorize("hasPermission('user:detail') or #id == authentication.principal.id")
    public Result<List<String>> getUserPermissions(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long id) {
        
        List<String> permissions = userService.getUserPermissions(id);
        return Result.success(permissions);
    }

    /**
     * 检查用户名是否存在
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已存在")
    public Result<Boolean> checkUsername(
            @Parameter(description = "用户名", required = true) @RequestParam @NotEmpty String username,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = userService.existsByUsername(username, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 检查邮箱是否存在
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已存在")
    public Result<Boolean> checkEmail(
            @Parameter(description = "邮箱", required = true) @RequestParam @NotEmpty String email,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = userService.existsByEmail(email, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 检查手机号是否存在
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号", description = "检查手机号是否已存在")
    public Result<Boolean> checkPhone(
            @Parameter(description = "手机号", required = true) @RequestParam @NotEmpty String phone,
            @Parameter(description = "排除的用户ID") @RequestParam(required = false) Long excludeUserId) {
        
        boolean exists = userService.existsByPhone(phone, excludeUserId);
        return Result.success(exists);
    }

    /**
     * 统计用户数量
     */
    @GetMapping("/count")
    @Operation(summary = "统计用户数量", description = "根据条件统计用户数量")
    @PreAuthorize("hasPermission('user:list')")
    public Result<Long> countUsers(
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "部门ID") @RequestParam(required = false) Long departmentId) {
        
        Long count = userService.countUsers(status, departmentId);
        return Result.success(count);
    }

    /**
     * 根据角色ID查询用户列表
     */
    @GetMapping("/by-role/{roleId}")
    @Operation(summary = "根据角色查询用户", description = "根据角色ID查询用户列表")
    @PreAuthorize("hasPermission('user:list')")
    public Result<List<User>> getUsersByRoleId(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long roleId) {
        
        List<User> users = userService.getUsersByRoleId(roleId);
        return Result.success(users);
    }

    /**
     * 根据部门ID查询用户列表
     */
    @GetMapping("/by-department/{departmentId}")
    @Operation(summary = "根据部门查询用户", description = "根据部门ID查询用户列表")
    @PreAuthorize("hasPermission('user:list')")
    public Result<List<User>> getUsersByDepartmentId(
            @Parameter(description = "部门ID", required = true) @PathVariable @NotNull Long departmentId,
            @Parameter(description = "是否包含子部门") @RequestParam(defaultValue = "false") Boolean includeSubDept) {
        
        List<User> users = userService.getUsersByDepartmentId(departmentId, includeSubDept);
        return Result.success(users);
    }

    /**
     * 根据权限编码查询用户列表
     */
    @GetMapping("/by-permission/{permissionCode}")
    @Operation(summary = "根据权限查询用户", description = "根据权限编码查询用户列表")
    @PreAuthorize("hasPermission('user:list')")
    public Result<List<User>> getUsersByPermissionCode(
            @Parameter(description = "权限编码", required = true) @PathVariable @NotEmpty String permissionCode) {
        
        List<User> users = userService.getUsersByPermissionCode(permissionCode);
        return Result.success(users);
    }

    /**
     * 导入用户
     */
    @PostMapping("/import")
    @Operation(summary = "导入用户", description = "批量导入用户")
    @PreAuthorize("hasPermission('user:import')")
    public Result<UserService.ImportResult> importUsers(
            @Parameter(description = "用户列表", required = true) @RequestBody @NotEmpty List<User> users,
            @Parameter(description = "是否更新已存在的用户") @RequestParam(defaultValue = "false") boolean updateExisting) {
        
        UserService.ImportResult result = userService.importUsers(users, updateExisting);
        return Result.success(result);
    }

    /**
     * 导出用户
     */
    @PostMapping("/export")
    @Operation(summary = "导出用户", description = "导出用户数据")
    @PreAuthorize("hasPermission('user:export')")
    public Result<List<User>> exportUsers(
            @Parameter(description = "用户ID列表（为空则导出所有）") @RequestBody(required = false) List<Long> userIds) {
        
        List<User> users = userService.exportUsers(userIds);
        return Result.success(users);
    }
}