<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kingdee.integration.mapper.RoleMapper">

    <!-- 角色结果映射 -->
    <resultMap id="BaseResultMap" type="com.kingdee.integration.entity.Role">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="role_code" property="roleCode" jdbcType="VARCHAR"/>
        <result column="role_name" property="roleName" jdbcType="VARCHAR"/>
        <result column="role_type" property="roleType" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="sort_order" property="sortOrder" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="is_system" property="isSystem" jdbcType="BOOLEAN"/>
        <result column="parent_id" property="parentId" jdbcType="BIGINT"/>
        <result column="level_path" property="levelPath" jdbcType="VARCHAR"/>
        <result column="data_scope" property="dataScope" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="create_by" property="createBy" jdbcType="BIGINT"/>
        <result column="update_by" property="updateBy" jdbcType="BIGINT"/>
        <result column="deleted" property="deleted" jdbcType="BOOLEAN"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
        <result column="tenant_id" property="tenantId" jdbcType="BIGINT"/>
        <result column="remark" property="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 角色详情结果映射（包含权限信息） -->
    <resultMap id="RoleWithPermissionsMap" type="com.kingdee.integration.entity.Role" extends="BaseResultMap">
        <collection property="permissions" ofType="String">
            <result column="permission_code"/>
        </collection>
    </resultMap>

    <!-- 基础列定义 -->
    <sql id="Base_Column_List">
        r.id, r.role_code, r.role_name, r.role_type, r.description, r.sort_order,
        r.status, r.is_system, r.parent_id, r.level_path, r.data_scope,
        r.create_time, r.update_time, r.create_by, r.update_by, r.deleted, r.version, r.tenant_id, r.remark
    </sql>

    <!-- 根据角色编码查询角色 -->
    <select id="findByRoleCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.role_code = #{roleCode}
        AND r.deleted = false
    </select>

    <!-- 根据角色名称查询角色 -->
    <select id="findByRoleName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.role_name = #{roleName}
        AND r.deleted = false
    </select>

    <!-- 分页查询角色列表 -->
    <select id="pageRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.deleted = false
        <if test="roleCode != null and roleCode != ''">
            AND r.role_code LIKE CONCAT('%', #{roleCode}, '%')
        </if>
        <if test="roleName != null and roleName != ''">
            AND r.role_name LIKE CONCAT('%', #{roleName}, '%')
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        <if test="roleType != null and roleType != ''">
            AND r.role_type = #{roleType}
        </if>
        ORDER BY r.sort_order ASC, r.create_time DESC
    </select>

    <!-- 根据用户ID查询角色列表 -->
    <select id="getRolesByUserId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        INNER JOIN sys_user_role ur ON r.id = ur.role_id
        WHERE ur.user_id = #{userId}
        AND r.deleted = false
        AND ur.deleted = false
        AND r.status = 1
        AND ur.status = 1
        ORDER BY r.sort_order ASC
    </select>

    <!-- 获取所有启用的角色 -->
    <select id="getEnabledRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.deleted = false
        AND r.status = 1
        ORDER BY r.sort_order ASC, r.create_time DESC
    </select>

    <!-- 根据角色类型查询角色列表 -->
    <select id="getRolesByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.deleted = false
        AND r.role_type = #{roleType}
        AND r.status = 1
        ORDER BY r.sort_order ASC
    </select>

    <!-- 获取角色权限编码列表 -->
    <select id="getRolePermissions" resultType="String">
        SELECT p.permission_code
        FROM sys_permission p
        INNER JOIN sys_role_permission rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
        AND p.deleted = false
        AND rp.deleted = false
        AND p.status = 1
        AND rp.status = 1
    </select>

    <!-- 获取角色权限ID列表 -->
    <select id="getRolePermissionIds" resultType="Long">
        SELECT rp.permission_id
        FROM sys_role_permission rp
        INNER JOIN sys_permission p ON rp.permission_id = p.id
        WHERE rp.role_id = #{roleId}
        AND rp.deleted = false
        AND p.deleted = false
        AND rp.status = 1
        AND p.status = 1
    </select>

    <!-- 检查角色是否有指定权限 -->
    <select id="hasPermission" resultType="int">
        SELECT COUNT(1)
        FROM sys_role_permission rp
        INNER JOIN sys_permission p ON rp.permission_id = p.id
        WHERE rp.role_id = #{roleId}
        AND p.permission_code = #{permissionCode}
        AND rp.deleted = false
        AND p.deleted = false
        AND rp.status = 1
        AND p.status = 1
    </select>

    <!-- 检查角色编码是否存在 -->
    <select id="existsByRoleCode" resultType="int">
        SELECT COUNT(1)
        FROM sys_role r
        WHERE r.role_code = #{roleCode}
        AND r.deleted = false
        <if test="excludeRoleId != null">
            AND r.id != #{excludeRoleId}
        </if>
    </select>

    <!-- 检查角色名称是否存在 -->
    <select id="existsByRoleName" resultType="int">
        SELECT COUNT(1)
        FROM sys_role r
        WHERE r.role_name = #{roleName}
        AND r.deleted = false
        <if test="excludeRoleId != null">
            AND r.id != #{excludeRoleId}
        </if>
    </select>

    <!-- 统计角色数量 -->
    <select id="countRoles" resultType="Long">
        SELECT COUNT(1)
        FROM sys_role r
        WHERE r.deleted = false
        <if test="status != null">
            AND r.status = #{status}
        </if>
        <if test="roleType != null and roleType != ''">
            AND r.role_type = #{roleType}
        </if>
    </select>

    <!-- 获取角色用户数量 -->
    <select id="getRoleUserCount" resultType="Long">
        SELECT COUNT(DISTINCT ur.user_id)
        FROM sys_user_role ur
        INNER JOIN sys_user u ON ur.user_id = u.id
        WHERE ur.role_id = #{roleId}
        AND ur.deleted = false
        AND u.deleted = false
        AND ur.status = 1
        AND u.status = 1
    </select>

    <!-- 删除角色权限关联 -->
    <delete id="deleteRolePermissions">
        UPDATE sys_role_permission
        SET deleted = true, update_time = NOW()
        WHERE role_id = #{roleId}
        AND deleted = false
    </delete>

    <!-- 批量插入角色权限关联 -->
    <insert id="insertRolePermissions">
        INSERT INTO sys_role_permission (role_id, permission_id, grant_type, data_scope, status, create_time, create_by)
        VALUES
        <foreach collection="permissionIds" item="permissionId" separator=",">
            (#{roleId}, #{permissionId}, 'GRANT', 'ALL', 1, NOW(), 1)
        </foreach>
    </insert>

    <!-- 删除指定角色权限关联 -->
    <update id="deleteRolePermissionsByIds">
        UPDATE sys_role_permission
        SET deleted = true, update_time = NOW()
        WHERE role_id = #{roleId}
        AND permission_id IN
        <foreach collection="permissionIds" item="permissionId" open="(" close=")" separator=",">
            #{permissionId}
        </foreach>
        AND deleted = false
    </update>

    <!-- 更新角色状态 -->
    <update id="updateRoleStatus">
        UPDATE sys_role
        SET status = #{status}, update_time = NOW()
        WHERE id = #{roleId}
        AND deleted = false
    </update>

    <!-- 获取角色详情（包含权限信息） -->
    <select id="getRoleWithPermissions" resultMap="RoleWithPermissionsMap">
        SELECT <include refid="Base_Column_List"/>, p.permission_code
        FROM sys_role r
        LEFT JOIN sys_role_permission rp ON r.id = rp.role_id AND rp.deleted = false AND rp.status = 1
        LEFT JOIN sys_permission p ON rp.permission_id = p.id AND p.deleted = false AND p.status = 1
        WHERE r.id = #{roleId}
        AND r.deleted = false
    </select>

    <!-- 根据权限编码查询角色列表 -->
    <select id="getRolesByPermissionCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        INNER JOIN sys_role_permission rp ON r.id = rp.role_id
        INNER JOIN sys_permission p ON rp.permission_id = p.id
        WHERE p.permission_code = #{permissionCode}
        AND r.deleted = false
        AND rp.deleted = false
        AND p.deleted = false
        AND r.status = 1
        AND rp.status = 1
        AND p.status = 1
        ORDER BY r.sort_order ASC
    </select>

    <!-- 获取用户可分配的角色列表 -->
    <select id="getAssignableRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.deleted = false
        AND r.status = 1
        AND r.id NOT IN (
            SELECT ur.role_id
            FROM sys_user_role ur
            WHERE ur.user_id = #{userId}
            AND ur.deleted = false
            AND ur.status = 1
        )
        ORDER BY r.sort_order ASC
    </select>

    <!-- 检查角色是否被用户使用 -->
    <select id="checkRoleInUse" resultType="int">
        SELECT COUNT(1)
        FROM sys_user_role ur
        WHERE ur.role_id = #{roleId}
        AND ur.deleted = false
        AND ur.status = 1
    </select>

    <!-- 获取角色层级结构 -->
    <select id="getChildRoles" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM sys_role r
        WHERE r.parent_id = #{parentId}
        AND r.deleted = false
        ORDER BY r.sort_order ASC
    </select>

    <!-- 获取角色路径 -->
    <select id="getRolePath" resultType="String">
        SELECT r.level_path
        FROM sys_role r
        WHERE r.id = #{roleId}
        AND r.deleted = false
    </select>

</mapper>