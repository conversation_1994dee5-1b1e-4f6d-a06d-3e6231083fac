<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.kingdee.integration.mapper.DepartmentMapper">

    <!-- 部门结果映射 -->
    <resultMap id="BaseResultMap" type="com.kingdee.integration.entity.Department">
        <id column="id" property="id" />
        <result column="dept_code" property="deptCode" />
        <result column="dept_name" property="deptName" />
        <result column="dept_short_name" property="deptShortName" />
        <result column="parent_id" property="parentId" />
        <result column="dept_level" property="deptLevel" />
        <result column="dept_path" property="deptPath" />
        <result column="dept_type" property="deptType" />
        <result column="manager_id" property="managerId" />
        <result column="manager_name" property="managerName" />
        <result column="phone" property="phone" />
        <result column="email" property="email" />
        <result column="fax" property="fax" />
        <result column="address" property="address" />
        <result column="sort_order" property="sortOrder" />
        <result column="status" property="status" />
        <result column="description" property="description" />
        <result column="extra_attrs" property="extraAttrs" />
        <result column="create_by" property="createBy" />
        <result column="create_by_name" property="createByName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_by_name" property="updateByName" />
        <result column="update_time" property="updateTime" />
        <result column="is_deleted" property="isDeleted" />
        <result column="version" property="version" />
    </resultMap>

    <!-- 部门详情结果映射（包含父部门信息） -->
    <resultMap id="DepartmentWithParentResultMap" type="com.kingdee.integration.entity.Department" extends="BaseResultMap">
        <result column="parent_name" property="parentName" />
        <result column="user_count" property="userCount" />
        <result column="has_children" property="hasChildren" />
    </resultMap>

    <!-- 基础列定义 -->
    <sql id="Base_Column_List">
        id, dept_code, dept_name, dept_short_name, parent_id, dept_level, dept_path,
        dept_type, manager_id, manager_name, phone, email, fax, address, sort_order,
        status, description, extra_attrs, create_by, create_by_name, create_time,
        update_by, update_by_name, update_time, is_deleted, version
    </sql>

    <!-- 根据部门编码查询部门 -->
    <select id="selectByDeptCode" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE dept_code = #{deptCode} AND is_deleted = 0
    </select>

    <!-- 根据部门名称查询部门 -->
    <select id="selectByDeptName" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE dept_name = #{deptName} AND is_deleted = 0
    </select>

    <!-- 分页查询部门列表 -->
    <select id="selectDepartmentPage" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.is_deleted = 0
        <if test="deptName != null and deptName != ''">
            AND d.dept_name LIKE CONCAT('%', #{deptName}, '%')
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND d.dept_code LIKE CONCAT('%', #{deptCode}, '%')
        </if>
        <if test="deptType != null and deptType != ''">
            AND d.dept_type = #{deptType}
        </if>
        <if test="status != null">
            AND d.status = #{status}
        </if>
        <if test="parentId != null">
            AND d.parent_id = #{parentId}
        </if>
        ORDER BY d.sort_order ASC, d.create_time DESC
    </select>

    <!-- 获取部门树结构 -->
    <select id="selectDepartmentTree" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.is_deleted = 0 AND d.status = 1
        ORDER BY d.dept_level ASC, d.sort_order ASC
    </select>

    <!-- 获取启用的部门列表 -->
    <select id="selectEnabledDepartments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE status = 1 AND is_deleted = 0
        ORDER BY sort_order ASC, dept_name ASC
    </select>

    <!-- 根据部门类型查询部门列表 -->
    <select id="selectDepartmentsByType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE dept_type = #{deptType} AND is_deleted = 0
        ORDER BY sort_order ASC, dept_name ASC
    </select>

    <!-- 根据用户ID查询部门 -->
    <select id="selectDepartmentByUserId" resultMap="BaseResultMap">
        SELECT d.<include refid="Base_Column_List" />
        FROM sys_department d
        INNER JOIN sys_user u ON d.id = u.dept_id
        WHERE u.id = #{userId} AND d.is_deleted = 0 AND u.is_deleted = 0
    </select>

    <!-- 根据负责人ID查询部门列表 -->
    <select id="selectDepartmentsByManagerId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE manager_id = #{managerId} AND is_deleted = 0
        ORDER BY sort_order ASC, dept_name ASC
    </select>

    <!-- 获取子部门列表 -->
    <select id="selectChildDepartments" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.parent_id = #{parentId} AND d.is_deleted = 0
        ORDER BY d.sort_order ASC, d.dept_name ASC
    </select>

    <!-- 获取所有子部门ID（包括子子部门） -->
    <select id="selectAllChildDepartmentIds" resultType="java.lang.Long">
        WITH RECURSIVE dept_tree AS (
            SELECT id, parent_id, dept_name
            FROM sys_department
            WHERE parent_id = #{parentId} AND is_deleted = 0
            UNION ALL
            SELECT d.id, d.parent_id, d.dept_name
            FROM sys_department d
            INNER JOIN dept_tree dt ON d.parent_id = dt.id
            WHERE d.is_deleted = 0
        )
        SELECT id FROM dept_tree
    </select>

    <!-- 获取部门路径 -->
    <select id="selectDepartmentPath" resultType="java.lang.String">
        SELECT dept_path
        FROM sys_department
        WHERE id = #{deptId} AND is_deleted = 0
    </select>

    <!-- 检查部门编码是否存在 -->
    <select id="existsByDeptCode" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_department
        WHERE dept_code = #{deptCode} AND is_deleted = 0
    </select>

    <!-- 检查部门名称是否存在 -->
    <select id="existsByDeptName" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_department
        WHERE dept_name = #{deptName}
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND (parent_id IS NULL OR parent_id = 0)
        </if>
        AND is_deleted = 0
    </select>

    <!-- 统计部门数量 -->
    <select id="countDepartments" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_department
        WHERE is_deleted = 0
    </select>

    <!-- 根据类型统计部门数量 -->
    <select id="countDepartmentsByType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_department
        WHERE dept_type = #{deptType} AND is_deleted = 0
    </select>

    <!-- 根据状态统计部门数量 -->
    <select id="countDepartmentsByStatus" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_department
        WHERE status = #{status} AND is_deleted = 0
    </select>

    <!-- 获取最大排序号 -->
    <select id="selectMaxSortOrder" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(sort_order), 0)
        FROM sys_department
        WHERE is_deleted = 0
        <if test="parentId != null">
            AND parent_id = #{parentId}
        </if>
        <if test="parentId == null">
            AND (parent_id IS NULL OR parent_id = 0)
        </if>
    </select>

    <!-- 更新部门状态 -->
    <update id="updateDepartmentStatus">
        UPDATE sys_department
        SET status = #{status}, update_time = NOW()
        WHERE id = #{deptId} AND is_deleted = 0
    </update>

    <!-- 批量更新部门状态 -->
    <update id="batchUpdateStatus">
        UPDATE sys_department
        SET status = #{status}, update_time = NOW()
        WHERE id IN
        <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
            #{deptId}
        </foreach>
        AND is_deleted = 0
    </update>

    <!-- 获取部门详情（包含父部门信息） -->
    <select id="selectDepartmentWithParent" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.id = #{deptId} AND d.is_deleted = 0
    </select>

    <!-- 检查部门是否有子部门 -->
    <select id="hasChildDepartments" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_department
        WHERE parent_id = #{deptId} AND is_deleted = 0
    </select>

    <!-- 检查部门是否有用户 -->
    <select id="hasUsers" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM sys_user
        WHERE dept_id = #{deptId} AND is_deleted = 0
    </select>

    <!-- 获取部门用户数量 -->
    <select id="getDepartmentUserCount" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM sys_user
        WHERE dept_id = #{deptId} AND is_deleted = 0
    </select>

    <!-- 更新部门路径 -->
    <update id="updateDepartmentPath">
        UPDATE sys_department
        SET dept_path = #{deptPath}, update_time = NOW()
        WHERE id = #{deptId} AND is_deleted = 0
    </update>

    <!-- 批量更新子部门路径 -->
    <update id="batchUpdateChildDepartmentPaths">
        UPDATE sys_department
        SET dept_path = REPLACE(dept_path, #{oldPath}, #{newPath}), update_time = NOW()
        WHERE dept_path LIKE CONCAT(#{oldPath}, '%') AND is_deleted = 0
    </update>

    <!-- 更新部门层级 -->
    <update id="updateDepartmentLevel">
        UPDATE sys_department
        SET dept_level = #{deptLevel}, update_time = NOW()
        WHERE id = #{deptId} AND is_deleted = 0
    </update>

    <!-- 批量更新子部门层级 -->
    <update id="batchUpdateChildDepartmentLevels">
        UPDATE sys_department
        SET dept_level = dept_level + #{levelIncrement}, update_time = NOW()
        WHERE dept_path LIKE CONCAT(
            (SELECT dept_path FROM (SELECT dept_path FROM sys_department WHERE id = #{parentId}) t),
            '.%'
        ) AND is_deleted = 0
    </update>

    <!-- 根据部门路径查询部门列表 -->
    <select id="selectDepartmentsByPathPrefix" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE dept_path LIKE CONCAT(#{pathPrefix}, '%') AND is_deleted = 0
        ORDER BY dept_level ASC, sort_order ASC
    </select>

    <!-- 获取根部门列表 -->
    <select id="selectRootDepartments" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE (d.parent_id IS NULL OR d.parent_id = 0) AND d.is_deleted = 0
        ORDER BY d.sort_order ASC, d.dept_name ASC
    </select>

    <!-- 获取叶子部门列表 -->
    <select id="selectLeafDepartments" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department d
        WHERE d.is_deleted = 0
        AND NOT EXISTS (
            SELECT 1 FROM sys_department c
            WHERE c.parent_id = d.id AND c.is_deleted = 0
        )
        ORDER BY d.sort_order ASC, d.dept_name ASC
    </select>

    <!-- 根据层级查询部门列表 -->
    <select id="selectDepartmentsByLevel" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM sys_department
        WHERE dept_level = #{deptLevel} AND is_deleted = 0
        ORDER BY sort_order ASC, dept_name ASC
    </select>

    <!-- 获取部门层级结构 -->
    <select id="selectDepartmentHierarchy" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.is_deleted = 0
        <if test="maxLevel != null">
            AND d.dept_level <= #{maxLevel}
        </if>
        ORDER BY d.dept_level ASC, d.sort_order ASC
    </select>

    <!-- 搜索部门（支持模糊查询） -->
    <select id="searchDepartments" resultMap="DepartmentWithParentResultMap">
        SELECT d.<include refid="Base_Column_List" />,
               p.dept_name as parent_name,
               COALESCE(uc.user_count, 0) as user_count,
               CASE WHEN cc.child_count > 0 THEN 1 ELSE 0 END as has_children
        FROM sys_department d
        LEFT JOIN sys_department p ON d.parent_id = p.id AND p.is_deleted = 0
        LEFT JOIN (
            SELECT dept_id, COUNT(*) as user_count
            FROM sys_user
            WHERE is_deleted = 0
            GROUP BY dept_id
        ) uc ON d.id = uc.dept_id
        LEFT JOIN (
            SELECT parent_id, COUNT(*) as child_count
            FROM sys_department
            WHERE is_deleted = 0
            GROUP BY parent_id
        ) cc ON d.id = cc.parent_id
        WHERE d.is_deleted = 0
        AND (
            d.dept_name LIKE CONCAT('%', #{keyword}, '%')
            OR d.dept_code LIKE CONCAT('%', #{keyword}, '%')
            OR d.manager_name LIKE CONCAT('%', #{keyword}, '%')
        )
        ORDER BY d.dept_level ASC, d.sort_order ASC
    </select>

    <!-- 获取用户可访问的部门列表 -->
    <select id="selectAccessibleDepartments" resultMap="BaseResultMap">
        SELECT DISTINCT d.<include refid="Base_Column_List" />
        FROM sys_department d
        INNER JOIN sys_user u ON (
            u.dept_id = d.id
            OR d.dept_path LIKE CONCAT(
                (SELECT dept_path FROM sys_department WHERE id = u.dept_id), '%'
            )
        )
        WHERE u.id = #{userId} AND d.is_deleted = 0 AND u.is_deleted = 0
        ORDER BY d.dept_level ASC, d.sort_order ASC
    </select>

    <!-- 获取部门统计信息 -->
    <select id="selectDepartmentStatistics" resultType="java.util.Map">
        SELECT
            'total' as type,
            COUNT(*) as count
        FROM sys_department
        WHERE is_deleted = 0
        UNION ALL
        SELECT
            'enabled' as type,
            COUNT(*) as count
        FROM sys_department
        WHERE status = 1 AND is_deleted = 0
        UNION ALL
        SELECT
            'disabled' as type,
            COUNT(*) as count
        FROM sys_department
        WHERE status = 0 AND is_deleted = 0
        UNION ALL
        SELECT
            dept_type as type,
            COUNT(*) as count
        FROM sys_department
        WHERE is_deleted = 0
        GROUP BY dept_type
    </select>

</mapper>