package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kingdee.integration.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 角色实体类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role")
public class Role extends BaseEntity {

    /**
     * 角色编码
     */
    @NotBlank(message = "角色编码不能为空")
    @Size(max = 50, message = "角色编码长度不能超过50个字符")
    @TableField("role_code")
    private String roleCode;

    /**
     * 角色名称
     */
    @NotBlank(message = "角色名称不能为空")
    @Size(max = 100, message = "角色名称长度不能超过100个字符")
    @TableField("role_name")
    private String roleName;

    /**
     * 角色描述
     */
    @Size(max = 500, message = "角色描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 角色类型（1：系统角色，2：业务角色）
     */
    @TableField("role_type")
    private Integer roleType;

    /**
     * 角色状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 数据权限范围（1：全部数据，2：部门数据，3：个人数据）
     */
    @TableField("data_scope")
    private Integer dataScope;

    /**
     * 是否内置角色（0：否，1：是）
     */
    @TableField("is_builtin")
    private Integer isBuiltin;

    // 非数据库字段
    
    /**
     * 权限列表
     */
    @TableField(exist = false)
    private java.util.List<Permission> permissions;

    /**
     * 用户数量
     */
    @TableField(exist = false)
    private Integer userCount;

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 是否系统角色
     */
    public boolean isSystemRole() {
        return this.roleType != null && this.roleType == 1;
    }

    /**
     * 是否内置角色
     */
    public boolean isBuiltinRole() {
        return this.isBuiltin != null && this.isBuiltin == 1;
    }
}