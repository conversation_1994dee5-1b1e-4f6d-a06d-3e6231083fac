package com.kingdee.integration.controller;

import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.dto.request.LoginRequest;
import com.kingdee.integration.dto.request.RefreshTokenRequest;
import com.kingdee.integration.dto.request.RegisterRequest;
import com.kingdee.integration.dto.response.LoginResponse;
import com.kingdee.integration.dto.response.UserInfoResponse;
import com.kingdee.integration.entity.User;
import com.kingdee.integration.service.AuthService;
import com.kingdee.integration.service.UserService;
import com.kingdee.integration.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 * 处理用户登录、注册、令牌刷新等认证相关操作
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    private final AuthService authService;
    private final UserService userService;
    private final JwtUtil jwtUtil;

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @param request      HTTP请求
     * @return 登录响应
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户通过用户名/邮箱/手机号和密码登录系统")
    public Result<LoginResponse> login(
            @Valid @RequestBody LoginRequest loginRequest,
            HttpServletRequest request) {
        
        log.info("用户登录请求: {}", loginRequest.getUsername());
        
        try {
            // 获取客户端IP
            String clientIp = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            
            // 执行登录
            LoginResponse response = authService.login(loginRequest, clientIp, userAgent);
            
            log.info("用户 {} 登录成功", loginRequest.getUsername());
            return Result.success(response, "登录成功");
            
        } catch (Exception e) {
            log.error("用户 {} 登录失败: {}", loginRequest.getUsername(), e.getMessage());
            return Result.error("登录失败: " + e.getMessage());
        }
    }

    /**
     * 用户注册
     *
     * @param registerRequest 注册请求
     * @param request         HTTP请求
     * @return 注册响应
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "新用户注册账号")
    public Result<UserInfoResponse> register(
            @Valid @RequestBody RegisterRequest registerRequest,
            HttpServletRequest request) {
        
        log.info("用户注册请求: {}", registerRequest.getUsername());
        
        try {
            // 获取客户端IP
            String clientIp = getClientIpAddress(request);
            
            // 执行注册
            User user = authService.register(registerRequest, clientIp);
            
            // 构建响应
            UserInfoResponse response = UserInfoResponse.fromUser(user);
            
            log.info("用户 {} 注册成功", registerRequest.getUsername());
            return Result.success(response, "注册成功");
            
        } catch (Exception e) {
            log.error("用户 {} 注册失败: {}", registerRequest.getUsername(), e.getMessage());
            return Result.error("注册失败: " + e.getMessage());
        }
    }

    /**
     * 刷新令牌
     *
     * @param refreshRequest 刷新令牌请求
     * @return 新的令牌
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用刷新令牌获取新的访问令牌")
    public Result<LoginResponse> refreshToken(
            @Valid @RequestBody RefreshTokenRequest refreshRequest) {
        
        log.info("令牌刷新请求");
        
        try {
            // 执行令牌刷新
            LoginResponse response = authService.refreshToken(refreshRequest.getRefreshToken());
            
            log.info("令牌刷新成功");
            return Result.success(response, "令牌刷新成功");
            
        } catch (Exception e) {
            log.error("令牌刷新失败: {}", e.getMessage());
            return Result.error("令牌刷新失败: " + e.getMessage());
        }
    }

    /**
     * 用户登出
     *
     * @param request HTTP请求
     * @return 登出响应
     */
    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "用户登出系统，使令牌失效")
    public Result<Void> logout(HttpServletRequest request) {
        
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getPrincipal() instanceof User) {
                User user = (User) authentication.getPrincipal();
                log.info("用户 {} 登出", user.getUsername());
            }
            
            // 获取JWT令牌
            String jwt = getJwtFromRequest(request);
            if (jwt != null) {
                // 将令牌加入黑名单
                authService.logout(jwt);
            }
            
            // 清除安全上下文
            SecurityContextHolder.clearContext();
            
            return Result.success("登出成功");
            
        } catch (Exception e) {
            log.error("登出失败: {}", e.getMessage());
            return Result.error("登出失败: " + e.getMessage());
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public Result<UserInfoResponse> getCurrentUser() {
        
        try {
            // 获取当前用户
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication == null || !(authentication.getPrincipal() instanceof User)) {
                return Result.error("用户未登录");
            }
            
            User user = (User) authentication.getPrincipal();
            
            // 重新查询用户信息以获取最新数据
            User currentUser = userService.findById(user.getId());
            if (currentUser == null) {
                return Result.error("用户不存在");
            }
            
            // 构建响应
            UserInfoResponse response = UserInfoResponse.fromUser(currentUser);
            
            return Result.success(response);
            
        } catch (Exception e) {
            log.error("获取当前用户信息失败: {}", e.getMessage());
            return Result.error("获取用户信息失败: " + e.getMessage());
        }
    }

    /**
     * 验证令牌
     *
     * @param token 令牌
     * @return 验证结果
     */
    @GetMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证JWT令牌是否有效")
    public Result<Boolean> validateToken(
            @Parameter(description = "JWT令牌") @RequestParam String token) {
        
        try {
            boolean isValid = jwtUtil.validateToken(token);
            return Result.success(isValid, isValid ? "令牌有效" : "令牌无效");
            
        } catch (Exception e) {
            log.error("令牌验证失败: {}", e.getMessage());
            return Result.success(false, "令牌无效");
        }
    }

    /**
     * 检查用户名是否可用
     *
     * @param username 用户名
     * @return 是否可用
     */
    @GetMapping("/check-username")
    @Operation(summary = "检查用户名", description = "检查用户名是否已被使用")
    public Result<Boolean> checkUsername(
            @Parameter(description = "用户名") @RequestParam String username) {
        
        try {
            boolean available = !userService.existsByUsername(username);
            return Result.success(available, available ? "用户名可用" : "用户名已被使用");
            
        } catch (Exception e) {
            log.error("检查用户名失败: {}", e.getMessage());
            return Result.error("检查用户名失败: " + e.getMessage());
        }
    }

    /**
     * 检查邮箱是否可用
     *
     * @param email 邮箱
     * @return 是否可用
     */
    @GetMapping("/check-email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已被使用")
    public Result<Boolean> checkEmail(
            @Parameter(description = "邮箱") @RequestParam String email) {
        
        try {
            boolean available = !userService.existsByEmail(email);
            return Result.success(available, available ? "邮箱可用" : "邮箱已被使用");
            
        } catch (Exception e) {
            log.error("检查邮箱失败: {}", e.getMessage());
            return Result.error("检查邮箱失败: " + e.getMessage());
        }
    }

    /**
     * 检查手机号是否可用
     *
     * @param phone 手机号
     * @return 是否可用
     */
    @GetMapping("/check-phone")
    @Operation(summary = "检查手机号", description = "检查手机号是否已被使用")
    public Result<Boolean> checkPhone(
            @Parameter(description = "手机号") @RequestParam String phone) {
        
        try {
            boolean available = !userService.existsByPhone(phone);
            return Result.success(available, available ? "手机号可用" : "手机号已被使用");
            
        } catch (Exception e) {
            log.error("检查手机号失败: {}", e.getMessage());
            return Result.error("检查手机号失败: " + e.getMessage());
        }
    }

    /**
     * 获取客户端IP地址
     *
     * @param request HTTP请求
     * @return IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * 从请求中获取JWT令牌
     *
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}