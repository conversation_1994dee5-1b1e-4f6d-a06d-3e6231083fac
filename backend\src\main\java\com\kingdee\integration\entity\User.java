package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.kingdee.integration.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

/**
 * 用户实体类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user")
public class User extends BaseEntity {

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50个字符之间")
    @Pattern(regexp = "^[a-zA-Z0-9_]+$", message = "用户名只能包含字母、数字和下划线")
    @TableField("username")
    private String username;

    /**
     * 密码（加密后）
     */
    @JsonIgnore
    @NotBlank(message = "密码不能为空")
    @TableField("password")
    private String password;

    /**
     * 真实姓名
     */
    @NotBlank(message = "真实姓名不能为空")
    @Size(max = 100, message = "真实姓名长度不能超过100个字符")
    @TableField("real_name")
    private String realName;

    /**
     * 邮箱
     */
    @Email(message = "邮箱格式不正确")
    @Size(max = 255, message = "邮箱长度不能超过255个字符")
    @TableField("email")
    private String email;

    /**
     * 手机号
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @TableField("phone")
    private String phone;

    /**
     * 头像URL
     */
    @TableField("avatar")
    private String avatar;

    /**
     * 性别（0：未知，1：男，2：女）
     */
    @TableField("gender")
    private Integer gender;

    /**
     * 生日
     */
    @TableField("birthday")
    private LocalDateTime birthday;

    /**
     * 部门ID
     */
    @TableField("department_id")
    private Long departmentId;

    /**
     * 职位
     */
    @Size(max = 100, message = "职位长度不能超过100个字符")
    @TableField("position")
    private String position;

    /**
     * 员工编号
     */
    @Size(max = 50, message = "员工编号长度不能超过50个字符")
    @TableField("employee_no")
    private String employeeNo;

    /**
     * 账户状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否锁定（0：未锁定，1：已锁定）
     */
    @TableField("locked")
    private Integer locked;

    /**
     * 密码过期时间
     */
    @TableField("password_expire_time")
    private LocalDateTime passwordExpireTime;

    /**
     * 最后登录时间
     */
    @TableField("last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @TableField("last_login_ip")
    private String lastLoginIp;

    /**
     * 登录失败次数
     */
    @TableField("login_failure_count")
    private Integer loginFailureCount;

    /**
     * 账户锁定时间
     */
    @TableField("lock_time")
    private LocalDateTime lockTime;

    /**
     * 用户类型（1：系统用户，2：普通用户）
     */
    @TableField("user_type")
    private Integer userType;

    /**
     * 语言偏好
     */
    @TableField("language")
    private String language;

    /**
     * 时区
     */
    @TableField("timezone")
    private String timezone;

    /**
     * 主题偏好
     */
    @TableField("theme")
    private String theme;

    /**
     * 扩展属性（JSON格式）
     */
    @TableField("extra_data")
    private String extraData;

    // 非数据库字段
    
    /**
     * 部门名称
     */
    @TableField(exist = false)
    private String departmentName;

    /**
     * 角色列表
     */
    @TableField(exist = false)
    private java.util.List<String> roles;

    /**
     * 权限列表
     */
    @TableField(exist = false)
    private java.util.List<String> permissions;

    /**
     * 是否为超级管理员
     */
    public boolean isAdmin() {
        return "admin".equals(this.username) || (this.userType != null && this.userType == 1);
    }

    /**
     * 是否账户启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 是否账户未锁定
     */
    public boolean isAccountNonLocked() {
        return this.locked == null || this.locked == 0;
    }

    /**
     * 是否密码未过期
     */
    public boolean isPasswordNonExpired() {
        return this.passwordExpireTime == null || this.passwordExpireTime.isAfter(LocalDateTime.now());
    }
}