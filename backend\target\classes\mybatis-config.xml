<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration PUBLIC "-//mybatis.org//DTD Config 3.0//EN" "http://mybatis.org/dtd/mybatis-3-config.dtd">
<configuration>
    <!-- 设置 -->
    <settings>
        <!-- 开启驼峰命名转换 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 开启延迟加载 -->
        <setting name="lazyLoadingEnabled" value="true"/>
        <!-- 设置积极加载改为消极加载即按需加载 -->
        <setting name="aggressiveLazyLoading" value="false"/>
        <!-- 开启二级缓存 -->
        <setting name="cacheEnabled" value="true"/>
        <!-- 设置超时时间 -->
        <setting name="defaultStatementTimeout" value="60"/>
        <!-- 设置获取数据的默认大小 -->
        <setting name="defaultFetchSize" value="100"/>
        <!-- 允许在嵌套语句中使用分页 -->
        <setting name="safeRowBoundsEnabled" value="false"/>
        <!-- 允许在嵌套语句中使用结果处理器 -->
        <setting name="safeResultHandlerEnabled" value="true"/>
        <!-- 是否开启自动驼峰命名规则映射 -->
        <setting name="mapUnderscoreToCamelCase" value="true"/>
        <!-- 本地缓存机制 -->
        <setting name="localCacheScope" value="SESSION"/>
        <!-- 数据库超时时间 -->
        <setting name="jdbcTypeForNull" value="OTHER"/>
        <!-- 延迟加载的触发方法 -->
        <setting name="lazyLoadTriggerMethods" value="equals,clone,hashCode,toString"/>
        <!-- 指定 MyBatis 应如何自动映射列到字段或属性 -->
        <setting name="autoMappingBehavior" value="PARTIAL"/>
        <!-- 指定发现自动映射目标未知列（或者未知属性类型）的行为 -->
        <setting name="autoMappingUnknownColumnBehavior" value="WARNING"/>
        <!-- 配置默认的执行器 -->
        <setting name="defaultExecutorType" value="SIMPLE"/>
        <!-- 指定 MyBatis 增加到日志名称的前缀 -->
        <setting name="logPrefix" value="dao."/>
        <!-- 指定 MyBatis 所用日志的具体实现 -->
        <setting name="logImpl" value="SLF4J"/>
        <!-- 指定动态 SQL 生成的默认语言 -->
        <setting name="defaultScriptingLanguage" value="org.apache.ibatis.scripting.xmltags.XMLLanguageDriver"/>
        <!-- 指定当结果集中值为 null 的时候是否调用映射对象的 setter（map 对象时为 put）方法 -->
        <setting name="callSettersOnNulls" value="false"/>
        <!-- 指定 MyBatis 返回值为 null 时调用 setter 或者 Map 的 put 方法 -->
        <setting name="returnInstanceForEmptyRow" value="false"/>
        <!-- 是否允许单一语句返回多结果集 -->
        <setting name="multipleResultSetsEnabled" value="true"/>
        <!-- 使用列标签代替列名 -->
        <setting name="useColumnLabel" value="true"/>
        <!-- 允许 JDBC 支持自动生成主键 -->
        <setting name="useGeneratedKeys" value="true"/>
    </settings>

    <!-- 类型别名 -->
    <typeAliases>
        <!-- 实体类包路径 -->
        <package name="com.kingdee.integration.entity"/>
    </typeAliases>

    <!-- 类型处理器 -->
    <typeHandlers>
        <!-- 自定义类型处理器 -->
        <!-- <typeHandler handler="com.kingdee.integration.config.mybatis.JsonTypeHandler"/> -->
    </typeHandlers>

    <!-- 插件 -->
    <plugins>
        <!-- 分页插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.PaginationInterceptor">
            <!-- 设置请求的页面大于最大页后操作， true调回到首页，false 继续请求  默认false -->
            <property name="overflow" value="false"/>
            <!-- 设置最大单页限制数量，默认 500 条，-1 不受限制 -->
            <property name="limit" value="500"/>
            <!-- 开启 count 的 join 优化,只针对部分 left join -->
            <property name="optimizeJoin" value="true"/>
        </plugin>
        
        <!-- 性能分析插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.PerformanceInterceptor">
            <!-- SQL 执行性能分析，开发环境使用，线上不推荐。 maxTime 指的是 sql 最大执行时长 -->
            <property name="maxTime" value="100"/>
            <!-- SQL是否格式化 默认false -->
            <property name="format" value="true"/>
        </plugin>
        
        <!-- 乐观锁插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.OptimisticLockerInterceptor"/>
        
        <!-- SQL执行效率插件 -->
        <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.SqlExplainInterceptor">
            <!-- 停止解析以下多表 -->
            <property name="stopProceed" value="false"/>
        </plugin>
        
        <!-- 动态表名插件 -->
        <!-- <plugin interceptor="com.baomidou.mybatisplus.extension.plugins.DynamicTableNameInterceptor"/> -->
        
        <!-- 数据权限插件 -->
        <!-- <plugin interceptor="com.kingdee.integration.config.mybatis.DataScopeInterceptor"/> -->
        
        <!-- SQL日志插件 -->
        <!-- <plugin interceptor="com.kingdee.integration.config.mybatis.SqlLogInterceptor"/> -->
    </plugins>

    <!-- 环境配置 -->
    <environments default="development">
        <environment id="development">
            <transactionManager type="JDBC"/>
            <dataSource type="POOLED">
                <property name="driver" value="com.mysql.cj.jdbc.Driver"/>
                <property name="url" value="*****************************************************************************************************************************************************************************"/>
                <property name="username" value="root"/>
                <property name="password" value="123456"/>
            </dataSource>
        </environment>
    </environments>

    <!-- 映射器 -->
    <mappers>
        <!-- 扫描映射文件 -->
        <package name="com.kingdee.integration.mapper"/>
    </mappers>
</configuration>