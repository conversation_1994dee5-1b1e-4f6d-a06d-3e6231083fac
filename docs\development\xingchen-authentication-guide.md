# XingChen (星辰) 认证开发指南

本文档详细介绍了如何在 Link-XingChen 项目中集成和使用金蝶云星辰的认证功能。

## 概述

金蝶云星辰采用基于 OAuth 2.0 的认证机制，通过 API 网关进行统一的身份验证和授权管理。本指南将帮助开发者快速集成认证功能并实现安全的 API 调用。

## 认证流程

### 1. 认证架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   客户端应用     │    │   API 网关      │    │   星辰系统      │
│                │    │                │    │                │
│  1. 发起认证请求 │───▶│  2. 验证客户端   │───▶│  3. 验证应用信息 │
│                │    │     凭证        │    │                │
│  6. 获得访问令牌 │◀───│  5. 返回令牌    │◀───│  4. 生成访问令牌 │
│                │    │                │    │                │
│  7. 使用令牌调用 │───▶│  8. 验证令牌    │───▶│  9. 处理业务请求 │
│     业务 API    │    │                │    │                │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 2. 认证步骤详解

1. **客户端配置**: 配置 `client_id` 和 `client_secret`
2. **初始化网关客户端**: 使用配置信息初始化 API 网关客户端
3. **构建认证请求**: 创建包含应用密钥和签名的认证请求
4. **发送认证请求**: 调用认证接口
5. **获取访问令牌**: 解析响应获取访问令牌
6. **使用令牌**: 在后续 API 调用中使用访问令牌

## 核心代码示例

### 1. 基础认证示例

```java
/**
 * 基础认证示例
 * 展示最简单的认证流程
 */
@Test
public void basicAuthenticationExample() throws Exception {
    // 配置 API 网关客户端
    ApigwConfig config = new ApigwConfig();
    config.setClientID("your-client-id");
    config.setClientSecret("your-client-secret");
    
    // 初始化客户端
    ApigwClient apigwClient = ApigwClient.getInstance();
    apigwClient.init(config);
    
    // 构建认证请求
    ApiRequest request = new ApiRequest(
        HttpMethod.GET, 
        "api.kingdee.com",
        "/jdyconnector/app_management/kingdee_auth_token"
    );
    
    // 设置请求参数
    Map<String, String> params = new HashMap<>();
    params.put("app_key", "your-app-key");
    params.put("app_signature", "your-app-signature");
    request.setQuerys(params);
    request.setBodyJson(JSONObject.toJSONString("").getBytes());
    
    // 发送请求并获取结果
    ApiResult result = apigwClient.send(request);
    
    // 处理结果
    if (result.isSuccess()) {
        String token = result.getToken();
        System.out.println("认证成功，访问令牌：" + token);
    } else {
        System.err.println("认证失败：" + result.getErrorMessage());
    }
}
```

### 2. Spring Boot 集成示例

```java
/**
 * 星辰认证服务
 * 在 Spring Boot 项目中的集成示例
 */
@Service
@Slf4j
public class XingChenAuthenticationService {
    
    @Value("${xingchen.client-id}")
    private String clientId;
    
    @Value("${xingchen.client-secret}")
    private String clientSecret;
    
    @Value("${xingchen.app-key}")
    private String appKey;
    
    @Value("${xingchen.app-signature}")
    private String appSignature;
    
    private ApigwClient apigwClient;
    
    @PostConstruct
    public void init() {
        try {
            ApigwConfig config = new ApigwConfig();
            config.setClientID(clientId);
            config.setClientSecret(clientSecret);
            
            apigwClient = ApigwClient.getInstance();
            apigwClient.init(config);
            
            log.info("XingChen API 网关客户端初始化成功");
        } catch (Exception e) {
            log.error("XingChen API 网关客户端初始化失败", e);
            throw new RuntimeException("认证服务初始化失败", e);
        }
    }
    
    /**
     * 获取访问令牌
     * @return 访问令牌
     */
    public String getAccessToken() {
        try {
            ApiRequest request = new ApiRequest(
                HttpMethod.GET, 
                "api.kingdee.com",
                "/jdyconnector/app_management/kingdee_auth_token"
            );
            
            Map<String, String> params = new HashMap<>();
            params.put("app_key", appKey);
            params.put("app_signature", appSignature);
            request.setQuerys(params);
            request.setBodyJson(JSONObject.toJSONString("").getBytes());
            
            ApiResult result = apigwClient.send(request);
            
            if (result.isSuccess()) {
                String token = result.getToken();
                log.info("获取访问令牌成功");
                return token;
            } else {
                log.error("获取访问令牌失败: {}", result.getErrorMessage());
                throw new RuntimeException("获取访问令牌失败: " + result.getErrorMessage());
            }
            
        } catch (Exception e) {
            log.error("获取访问令牌过程中发生异常", e);
            throw new RuntimeException("认证异常: " + e.getMessage(), e);
        }
    }
}
```

### 3. 配置文件示例

```yaml
# application.yml
xingchen:
  # 基础认证配置
  client-id: ${XINGCHEN_CLIENT_ID:your-client-id}
  client-secret: ${XINGCHEN_CLIENT_SECRET:your-client-secret}
  app-key: ${XINGCHEN_APP_KEY:your-app-key}
  app-signature: ${XINGCHEN_APP_SIGNATURE:your-app-signature}
  
  # API 网关配置
  gateway:
    base-url: https://api.kingdee.com
    timeout: 30000
    retry-times: 3
    
  # 令牌管理配置
  token:
    cache-enabled: true
    cache-ttl: 3600  # 令牌缓存时间（秒）
    refresh-threshold: 300  # 令牌刷新阈值（秒）
```

## 高级功能

### 1. 令牌缓存管理

```java
@Component
public class TokenCacheManager {
    
    private final RedisTemplate<String, String> redisTemplate;
    private static final String TOKEN_CACHE_KEY = "xingchen:token:";
    
    public TokenCacheManager(RedisTemplate<String, String> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    /**
     * 缓存访问令牌
     */
    public void cacheToken(String appKey, String token, long ttl) {
        String key = TOKEN_CACHE_KEY + appKey;
        redisTemplate.opsForValue().set(key, token, Duration.ofSeconds(ttl));
    }
    
    /**
     * 获取缓存的令牌
     */
    public String getCachedToken(String appKey) {
        String key = TOKEN_CACHE_KEY + appKey;
        return redisTemplate.opsForValue().get(key);
    }
    
    /**
     * 清除缓存的令牌
     */
    public void clearCachedToken(String appKey) {
        String key = TOKEN_CACHE_KEY + appKey;
        redisTemplate.delete(key);
    }
}
```

### 2. 自动令牌刷新

```java
@Component
@Slf4j
public class TokenRefreshScheduler {
    
    private final XingChenAuthenticationService authService;
    private final TokenCacheManager tokenCacheManager;
    
    public TokenRefreshScheduler(XingChenAuthenticationService authService,
                               TokenCacheManager tokenCacheManager) {
        this.authService = authService;
        this.tokenCacheManager = tokenCacheManager;
    }
    
    /**
     * 定时刷新令牌
     * 每30分钟执行一次
     */
    @Scheduled(fixedRate = 1800000) // 30分钟
    public void refreshToken() {
        try {
            log.info("开始刷新 XingChen 访问令牌");
            
            String newToken = authService.getAccessToken();
            tokenCacheManager.cacheToken("default", newToken, 3600);
            
            log.info("XingChen 访问令牌刷新成功");
        } catch (Exception e) {
            log.error("XingChen 访问令牌刷新失败", e);
        }
    }
}
```

## 错误处理

### 1. 常见错误码

| 错误码 | 错误信息 | 解决方案 |
|--------|----------|----------|
| 401 | 客户端认证失败 | 检查 client_id 和 client_secret |
| 403 | 应用签名验证失败 | 检查 app_key 和 app_signature |
| 429 | 请求频率超限 | 实现请求限流和重试机制 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 2. 异常处理最佳实践

```java
@Component
public class XingChenErrorHandler {
    
    private static final Logger log = LoggerFactory.getLogger(XingChenErrorHandler.class);
    
    public void handleAuthenticationError(ApiResult result) {
        int errorCode = result.getErrorCode();
        String errorMessage = result.getErrorMessage();
        
        switch (errorCode) {
            case 401:
                log.error("客户端认证失败: {}", errorMessage);
                throw new AuthenticationException("客户端认证失败，请检查 client_id 和 client_secret");
                
            case 403:
                log.error("应用签名验证失败: {}", errorMessage);
                throw new AuthenticationException("应用签名验证失败，请检查 app_key 和 app_signature");
                
            case 429:
                log.warn("请求频率超限: {}", errorMessage);
                throw new RateLimitException("请求频率超限，请稍后重试");
                
            default:
                log.error("认证过程中发生未知错误: {} - {}", errorCode, errorMessage);
                throw new RuntimeException("认证失败: " + errorMessage);
        }
    }
}
```

## 安全最佳实践

### 1. 敏感信息保护

- 使用环境变量存储 `client_secret`
- 不要在代码中硬编码敏感信息
- 使用加密配置文件
- 定期轮换密钥

### 2. 令牌安全

- 设置合理的令牌过期时间
- 实现令牌自动刷新机制
- 在令牌泄露时及时撤销
- 使用 HTTPS 传输令牌

### 3. 日志安全

- 不要在日志中记录敏感信息
- 对令牌进行脱敏处理
- 设置合适的日志级别
- 定期清理日志文件

## 测试指南

### 1. 单元测试示例

```java
@SpringBootTest
class XingChenAuthenticationServiceTest {
    
    @Autowired
    private XingChenAuthenticationService authService;
    
    @Test
    void testGetAccessToken() {
        // 测试获取访问令牌
        String token = authService.getAccessToken();
        assertNotNull(token);
        assertFalse(token.isEmpty());
    }
    
    @Test
    void testTokenCaching() {
        // 测试令牌缓存功能
        String token1 = authService.getAccessToken();
        String token2 = authService.getAccessToken();
        assertEquals(token1, token2); // 应该返回缓存的令牌
    }
}
```

### 2. 集成测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "xingchen.client-id=test-client-id",
    "xingchen.client-secret=test-client-secret"
})
class XingChenIntegrationTest {
    
    @Test
    void testFullAuthenticationFlow() {
        // 测试完整的认证流程
        // 包括配置初始化、令牌获取、API 调用等
    }
}
```

## 故障排查

### 1. 常见问题

**问题**: 认证失败，返回 401 错误
**解决**: 检查 client_id 和 client_secret 是否正确

**问题**: 应用签名验证失败
**解决**: 确认 app_key 和 app_signature 的生成算法正确

**问题**: 网络超时
**解决**: 检查网络连接，调整超时配置

### 2. 调试技巧

- 启用详细日志记录
- 使用网络抓包工具分析请求
- 检查配置文件的参数设置
- 验证 SDK 版本兼容性

## 参考资料

- [金蝶云星辰开发者文档](https://developer.kingdee.com)
- [API 网关使用指南](https://docs.kingdee.com/apigw)
- [OAuth 2.0 规范](https://tools.ietf.org/html/rfc6749)
- [项目需求文档](../../requirements.md)
