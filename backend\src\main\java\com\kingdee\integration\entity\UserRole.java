package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kingdee.integration.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 用户角色关联实体类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_user_role")
public class UserRole extends BaseEntity {

    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    @TableField("user_id")
    private Long userId;

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    @TableField("role_id")
    private Long roleId;

    /**
     * 分配类型（1：直接分配，2：继承分配）
     */
    @TableField("assign_type")
    private Integer assignType;

    /**
     * 分配来源（如部门ID、岗位ID等）
     */
    @TableField("assign_source")
    private Long assignSource;

    /**
     * 生效时间
     */
    @TableField("effective_time")
    private java.time.LocalDateTime effectiveTime;

    /**
     * 失效时间
     */
    @TableField("expiry_time")
    private java.time.LocalDateTime expiryTime;

    /**
     * 状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    // 非数据库字段
    
    /**
     * 用户名
     */
    @TableField(exist = false)
    private String username;

    /**
     * 用户真实姓名
     */
    @TableField(exist = false)
    private String realName;

    /**
     * 角色编码
     */
    @TableField(exist = false)
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 构造函数
     */
    public UserRole() {}

    public UserRole(Long userId, Long roleId) {
        this.userId = userId;
        this.roleId = roleId;
        this.assignType = 1; // 默认直接分配
        this.status = 1; // 默认启用
    }

    public UserRole(Long userId, Long roleId, Integer assignType) {
        this.userId = userId;
        this.roleId = roleId;
        this.assignType = assignType;
        this.status = 1; // 默认启用
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 是否直接分配
     */
    public boolean isDirectAssign() {
        return this.assignType != null && this.assignType == 1;
    }

    /**
     * 是否继承分配
     */
    public boolean isInheritAssign() {
        return this.assignType != null && this.assignType == 2;
    }

    /**
     * 是否在有效期内
     */
    public boolean isEffective() {
        java.time.LocalDateTime now = java.time.LocalDateTime.now();
        
        // 检查生效时间
        if (this.effectiveTime != null && now.isBefore(this.effectiveTime)) {
            return false;
        }
        
        // 检查失效时间
        if (this.expiryTime != null && now.isAfter(this.expiryTime)) {
            return false;
        }
        
        return true;
    }
}