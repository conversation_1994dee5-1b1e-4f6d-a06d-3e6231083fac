package com.kingdee.integration.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误 4xx
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权访问"),
    FORBIDDEN(403, "禁止访问"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    VALIDATION_ERROR(422, "参数验证失败"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误 5xx
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误 6xxx
    BUSINESS_ERROR(6000, "业务处理失败"),
    DATA_NOT_FOUND(6001, "数据不存在"),
    DATA_ALREADY_EXISTS(6002, "数据已存在"),
    DATA_INTEGRITY_VIOLATION(6003, "数据完整性约束违反"),
    OPERATION_NOT_ALLOWED(6004, "操作不被允许"),
    RESOURCE_LOCKED(6005, "资源被锁定"),
    WORKFLOW_ERROR(6006, "工作流处理错误"),

    // 认证授权错误 7xxx
    LOGIN_FAILED(7001, "登录失败"),
    TOKEN_EXPIRED(7002, "令牌已过期"),
    TOKEN_INVALID(7003, "令牌无效"),
    PERMISSION_DENIED(7004, "权限不足"),
    ACCOUNT_LOCKED(7005, "账户被锁定"),
    ACCOUNT_DISABLED(7006, "账户已禁用"),
    PASSWORD_EXPIRED(7007, "密码已过期"),
    CAPTCHA_ERROR(7008, "验证码错误"),

    // 集成错误 8xxx
    INTEGRATION_ERROR(8000, "系统集成错误"),
    KINGDEE_API_ERROR(8001, "金蝶API调用失败"),
    SYNC_ERROR(8002, "数据同步失败"),
    MAPPING_ERROR(8003, "数据映射错误"),
    EXTERNAL_SERVICE_ERROR(8004, "外部服务调用失败"),
    WEBHOOK_ERROR(8005, "Webhook处理失败"),
    MESSAGE_QUEUE_ERROR(8006, "消息队列错误"),

    // 文件处理错误 9xxx
    FILE_UPLOAD_ERROR(9001, "文件上传失败"),
    FILE_DOWNLOAD_ERROR(9002, "文件下载失败"),
    FILE_NOT_FOUND(9003, "文件不存在"),
    FILE_SIZE_EXCEEDED(9004, "文件大小超出限制"),
    FILE_TYPE_NOT_SUPPORTED(9005, "文件类型不支持"),
    FILE_PROCESSING_ERROR(9006, "文件处理失败");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态消息
     */
    private final String message;

    /**
     * 根据状态码获取枚举
     *
     * @param code 状态码
     * @return ResultCode
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }

    /**
     * 判断是否为成功状态
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isSuccess(Integer code) {
        return SUCCESS.getCode().equals(code);
    }

    /**
     * 判断是否为客户端错误
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isClientError(Integer code) {
        return code >= 400 && code < 500;
    }

    /**
     * 判断是否为服务器错误
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isServerError(Integer code) {
        return code >= 500 && code < 600;
    }

    /**
     * 判断是否为业务错误
     *
     * @param code 状态码
     * @return boolean
     */
    public static boolean isBusinessError(Integer code) {
        return code >= 6000 && code < 7000;
    }
}