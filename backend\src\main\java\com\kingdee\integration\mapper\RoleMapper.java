package com.kingdee.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 角色Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Role findByRoleCode(@Param("roleCode") String roleCode);

    /**
     * 根据角色名称查询角色
     *
     * @param roleName 角色名称
     * @return 角色信息
     */
    Role findByRoleName(@Param("roleName") String roleName);

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleCode 角色编码（模糊查询）
     * @param roleName 角色名称（模糊查询）
     * @param status 状态
     * @param roleType 角色类型
     * @return 角色分页列表
     */
    IPage<Role> pageRoles(Page<Role> page, 
                         @Param("roleCode") String roleCode,
                         @Param("roleName") String roleName,
                         @Param("status") Integer status,
                         @Param("roleType") String roleType);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(@Param("userId") Long userId);

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    List<Role> getEnabledRoles();

    /**
     * 根据角色类型查询角色列表
     *
     * @param roleType 角色类型
     * @return 角色列表
     */
    List<Role> getRolesByType(@Param("roleType") String roleType);

    /**
     * 获取角色权限编码列表
     *
     * @param roleId 角色ID
     * @return 权限编码列表
     */
    List<String> getRolePermissions(@Param("roleId") Long roleId);

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissionIds(@Param("roleId") Long roleId);

    /**
     * 检查角色是否有指定权限
     *
     * @param roleId 角色ID
     * @param permissionCode 权限编码
     * @return 权限数量（>0表示有权限）
     */
    int hasPermission(@Param("roleId") Long roleId, @Param("permissionCode") String permissionCode);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID
     * @return 角色数量
     */
    int existsByRoleCode(@Param("roleCode") String roleCode, @Param("excludeRoleId") Long excludeRoleId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID
     * @return 角色数量
     */
    int existsByRoleName(@Param("roleName") String roleName, @Param("excludeRoleId") Long excludeRoleId);

    /**
     * 统计角色数量
     *
     * @param status 状态
     * @param roleType 角色类型
     * @return 角色数量
     */
    Long countRoles(@Param("status") Integer status, @Param("roleType") String roleType);

    /**
     * 获取角色用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    Long getRoleUserCount(@Param("roleId") Long roleId);

    /**
     * 删除角色权限关联
     *
     * @param roleId 角色ID
     * @return 删除数量
     */
    int deleteRolePermissions(@Param("roleId") Long roleId);

    /**
     * 批量插入角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 插入数量
     */
    int insertRolePermissions(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);

    /**
     * 删除指定角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 删除数量
     */
    int deleteRolePermissionsByIds(@Param("roleId") Long roleId, @Param("permissionIds") List<Long> permissionIds);

    /**
     * 更新角色状态
     *
     * @param roleId 角色ID
     * @param status 状态
     * @return 更新数量
     */
    int updateRoleStatus(@Param("roleId") Long roleId, @Param("status") Integer status);

    /**
     * 获取角色详情（包含权限信息）
     *
     * @param roleId 角色ID
     * @return 角色详情
     */
    Role getRoleWithPermissions(@Param("roleId") Long roleId);

    /**
     * 根据权限编码查询角色列表
     *
     * @param permissionCode 权限编码
     * @return 角色列表
     */
    List<Role> getRolesByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 获取用户可分配的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getAssignableRoles(@Param("userId") Long userId);

    /**
     * 检查角色是否被用户使用
     *
     * @param roleId 角色ID
     * @return 使用数量
     */
    int checkRoleInUse(@Param("roleId") Long roleId);

    /**
     * 获取角色层级结构
     *
     * @param parentId 父角色ID
     * @return 子角色列表
     */
    List<Role> getChildRoles(@Param("parentId") Long parentId);

    /**
     * 获取角色路径
     *
     * @param roleId 角色ID
     * @return 角色路径
     */
    String getRolePath(@Param("roleId") Long roleId);
}