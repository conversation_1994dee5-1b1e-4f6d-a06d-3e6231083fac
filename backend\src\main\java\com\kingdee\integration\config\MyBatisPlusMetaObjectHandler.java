package com.kingdee.integration.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * MyBatis Plus 自动填充处理器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Component
public class MyBatisPlusMetaObjectHandler implements MetaObjectHandler {

    /**
     * 插入时自动填充
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");
        
        // 填充创建时间
        this.strictInsertFill(metaObject, "createdAt", LocalDateTime.class, LocalDateTime.now());
        // 填充更新时间
        this.strictInsertFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
        
        // 填充创建人ID（从当前登录用户获取）
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            this.strictInsertFill(metaObject, "createdBy", Long.class, currentUserId);
            this.strictInsertFill(metaObject, "updatedBy", Long.class, currentUserId);
        }
        
        // 填充租户ID
        String tenantId = getCurrentTenantId();
        if (tenantId != null) {
            this.strictInsertFill(metaObject, "tenantId", String.class, tenantId);
        }
        
        // 填充逻辑删除标识
        this.strictInsertFill(metaObject, "deleted", Integer.class, 0);
        
        // 填充版本号
        this.strictInsertFill(metaObject, "version", Integer.class, 1);
    }

    /**
     * 更新时自动填充
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");
        
        // 填充更新时间
        this.strictUpdateFill(metaObject, "updatedAt", LocalDateTime.class, LocalDateTime.now());
        
        // 填充更新人ID（从当前登录用户获取）
        Long currentUserId = getCurrentUserId();
        if (currentUserId != null) {
            this.strictUpdateFill(metaObject, "updatedBy", Long.class, currentUserId);
        }
    }

    /**
     * 获取当前登录用户ID
     * TODO: 集成Spring Security后实现
     *
     * @return 用户ID
     */
    private Long getCurrentUserId() {
        // 暂时返回系统用户ID，后续集成Spring Security后从SecurityContext获取
        return 1L;
    }

    /**
     * 获取当前租户ID
     * TODO: 实现多租户支持后完善
     *
     * @return 租户ID
     */
    private String getCurrentTenantId() {
        // 暂时返回默认租户ID，后续实现多租户支持
        return "default";
    }
}