package com.kingdee.integration.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.security.SecureRandom;
import java.util.regex.Pattern;

/**
 * 密码工具类
 * 提供密码加密、验证、强度检查等功能
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class PasswordUtil {

    private static final PasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
    
    private static final SecureRandom secureRandom = new SecureRandom();
    
    // 密码强度正则表达式
    private static final String WEAK_PASSWORD_PATTERN = "^[a-zA-Z]+$|^[0-9]+$|^[^a-zA-Z0-9]+$";
    private static final String MEDIUM_PASSWORD_PATTERN = "^(?=.*[a-zA-Z])(?=.*[0-9])[a-zA-Z0-9]{6,}$";
    private static final String STRONG_PASSWORD_PATTERN = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[^a-zA-Z0-9]).{8,}$";
    
    private static final Pattern WEAK_PATTERN = Pattern.compile(WEAK_PASSWORD_PATTERN);
    private static final Pattern MEDIUM_PATTERN = Pattern.compile(MEDIUM_PASSWORD_PATTERN);
    private static final Pattern STRONG_PATTERN = Pattern.compile(STRONG_PASSWORD_PATTERN);
    
    // 常见弱密码列表
    private static final String[] COMMON_WEAK_PASSWORDS = {
        "123456", "password", "123456789", "12345678", "12345", "1234567", "1234567890",
        "qwerty", "abc123", "111111", "123123", "admin", "letmein", "welcome",
        "monkey", "1234", "dragon", "pass", "master", "hello", "freedom", "whatever",
        "qazwsx", "trustno1", "jordan", "harley", "1234qwer", "sunshine", "iloveyou"
    };
    
    /**
     * 密码强度枚举
     */
    public enum PasswordStrength {
        WEAK("弱", 1),
        MEDIUM("中等", 2),
        STRONG("强", 3),
        VERY_STRONG("很强", 4);
        
        private final String description;
        private final int level;
        
        PasswordStrength(String description, int level) {
            this.description = description;
            this.level = level;
        }
        
        public String getDescription() {
            return description;
        }
        
        public int getLevel() {
            return level;
        }
    }
    
    /**
     * 加密密码
     *
     * @param rawPassword 原始密码
     * @return 加密后的密码
     */
    public static String encode(String rawPassword) {
        try {
            if (rawPassword == null || rawPassword.trim().isEmpty()) {
                throw new IllegalArgumentException("密码不能为空");
            }
            return passwordEncoder.encode(rawPassword);
        } catch (Exception e) {
            log.error("密码加密失败", e);
            throw new RuntimeException("密码加密失败", e);
        }
    }
    
    /**
     * 验证密码
     *
     * @param rawPassword     原始密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    public static boolean matches(String rawPassword, String encodedPassword) {
        try {
            if (rawPassword == null || encodedPassword == null) {
                return false;
            }
            return passwordEncoder.matches(rawPassword, encodedPassword);
        } catch (Exception e) {
            log.error("密码验证失败", e);
            return false;
        }
    }
    
    /**
     * 检查密码强度
     *
     * @param password 密码
     * @return 密码强度
     */
    public static PasswordStrength checkPasswordStrength(String password) {
        if (password == null || password.length() < 6) {
            return PasswordStrength.WEAK;
        }
        
        // 检查是否为常见弱密码
        if (isCommonWeakPassword(password)) {
            return PasswordStrength.WEAK;
        }
        
        // 检查密码复杂度
        int score = calculatePasswordScore(password);
        
        if (score >= 80) {
            return PasswordStrength.VERY_STRONG;
        } else if (score >= 60) {
            return PasswordStrength.STRONG;
        } else if (score >= 40) {
            return PasswordStrength.MEDIUM;
        } else {
            return PasswordStrength.WEAK;
        }
    }
    
    /**
     * 计算密码强度分数
     *
     * @param password 密码
     * @return 分数(0-100)
     */
    private static int calculatePasswordScore(String password) {
        int score = 0;
        
        // 长度分数
        if (password.length() >= 8) {
            score += 25;
        } else if (password.length() >= 6) {
            score += 10;
        }
        
        // 包含小写字母
        if (password.matches(".*[a-z].*")) {
            score += 5;
        }
        
        // 包含大写字母
        if (password.matches(".*[A-Z].*")) {
            score += 5;
        }
        
        // 包含数字
        if (password.matches(".*[0-9].*")) {
            score += 5;
        }
        
        // 包含特殊字符
        if (password.matches(".*[^a-zA-Z0-9].*")) {
            score += 10;
        }
        
        // 字符种类多样性
        int charTypes = 0;
        if (password.matches(".*[a-z].*")) charTypes++;
        if (password.matches(".*[A-Z].*")) charTypes++;
        if (password.matches(".*[0-9].*")) charTypes++;
        if (password.matches(".*[^a-zA-Z0-9].*")) charTypes++;
        
        score += charTypes * 5;
        
        // 长度额外加分
        if (password.length() >= 12) {
            score += 15;
        } else if (password.length() >= 10) {
            score += 10;
        }
        
        // 检查重复字符
        if (!hasRepeatingChars(password)) {
            score += 10;
        }
        
        // 检查连续字符
        if (!hasSequentialChars(password)) {
            score += 10;
        }
        
        return Math.min(score, 100);
    }
    
    /**
     * 检查是否为常见弱密码
     *
     * @param password 密码
     * @return 是否为弱密码
     */
    private static boolean isCommonWeakPassword(String password) {
        String lowerPassword = password.toLowerCase();
        for (String weakPassword : COMMON_WEAK_PASSWORDS) {
            if (lowerPassword.equals(weakPassword)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有重复字符
     *
     * @param password 密码
     * @return 是否有重复字符
     */
    private static boolean hasRepeatingChars(String password) {
        for (int i = 0; i < password.length() - 2; i++) {
            if (password.charAt(i) == password.charAt(i + 1) && 
                password.charAt(i) == password.charAt(i + 2)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 检查是否有连续字符
     *
     * @param password 密码
     * @return 是否有连续字符
     */
    private static boolean hasSequentialChars(String password) {
        for (int i = 0; i < password.length() - 2; i++) {
            char c1 = password.charAt(i);
            char c2 = password.charAt(i + 1);
            char c3 = password.charAt(i + 2);
            
            // 检查递增序列
            if (c2 == c1 + 1 && c3 == c2 + 1) {
                return true;
            }
            
            // 检查递减序列
            if (c2 == c1 - 1 && c3 == c2 - 1) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 生成随机密码
     *
     * @param length 密码长度
     * @return 随机密码
     */
    public static String generateRandomPassword(int length) {
        if (length < 6) {
            throw new IllegalArgumentException("密码长度不能小于6位");
        }
        
        String lowercase = "abcdefghijklmnopqrstuvwxyz";
        String uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        String numbers = "0123456789";
        String specialChars = "!@#$%^&*()_+-=[]{}|;:,.<>?";
        
        StringBuilder password = new StringBuilder();
        
        // 确保至少包含每种字符类型
        password.append(lowercase.charAt(secureRandom.nextInt(lowercase.length())));
        password.append(uppercase.charAt(secureRandom.nextInt(uppercase.length())));
        password.append(numbers.charAt(secureRandom.nextInt(numbers.length())));
        password.append(specialChars.charAt(secureRandom.nextInt(specialChars.length())));
        
        // 填充剩余长度
        String allChars = lowercase + uppercase + numbers + specialChars;
        for (int i = 4; i < length; i++) {
            password.append(allChars.charAt(secureRandom.nextInt(allChars.length())));
        }
        
        // 打乱字符顺序
        return shuffleString(password.toString());
    }
    
    /**
     * 打乱字符串
     *
     * @param input 输入字符串
     * @return 打乱后的字符串
     */
    private static String shuffleString(String input) {
        char[] chars = input.toCharArray();
        for (int i = chars.length - 1; i > 0; i--) {
            int j = secureRandom.nextInt(i + 1);
            char temp = chars[i];
            chars[i] = chars[j];
            chars[j] = temp;
        }
        return new String(chars);
    }
    
    /**
     * 验证密码格式
     *
     * @param password 密码
     * @return 验证结果
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        
        // 长度检查
        if (password.length() < 6 || password.length() > 50) {
            return false;
        }
        
        // 不能包含空格
        if (password.contains(" ")) {
            return false;
        }
        
        // 不能是常见弱密码
        if (isCommonWeakPassword(password)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取密码强度描述
     *
     * @param password 密码
     * @return 强度描述
     */
    public static String getPasswordStrengthDescription(String password) {
        PasswordStrength strength = checkPasswordStrength(password);
        int score = calculatePasswordScore(password);
        
        StringBuilder description = new StringBuilder();
        description.append("密码强度: ").append(strength.getDescription());
        description.append(" (分数: ").append(score).append("/100)");
        
        // 提供改进建议
        if (strength.getLevel() < 3) {
            description.append("\n建议: ");
            
            if (password.length() < 8) {
                description.append("增加密码长度至8位以上; ");
            }
            
            if (!password.matches(".*[a-z].*")) {
                description.append("包含小写字母; ");
            }
            
            if (!password.matches(".*[A-Z].*")) {
                description.append("包含大写字母; ");
            }
            
            if (!password.matches(".*[0-9].*")) {
                description.append("包含数字; ");
            }
            
            if (!password.matches(".*[^a-zA-Z0-9].*")) {
                description.append("包含特殊字符; ");
            }
        }
        
        return description.toString();
    }
    
    /**
     * 生成盐值
     *
     * @return 盐值
     */
    public static String generateSalt() {
        byte[] salt = new byte[16];
        secureRandom.nextBytes(salt);
        return java.util.Base64.getEncoder().encodeToString(salt);
    }
    
    /**
     * 检查密码是否过期
     *
     * @param lastPasswordChangeTime 上次密码修改时间
     * @param passwordValidityDays   密码有效期(天)
     * @return 是否过期
     */
    public static boolean isPasswordExpired(long lastPasswordChangeTime, int passwordValidityDays) {
        if (passwordValidityDays <= 0) {
            return false; // 永不过期
        }
        
        long currentTime = System.currentTimeMillis();
        long validityMillis = passwordValidityDays * 24L * 60L * 60L * 1000L;
        
        return (currentTime - lastPasswordChangeTime) > validityMillis;
    }
    
    /**
     * 计算密码过期剩余天数
     *
     * @param lastPasswordChangeTime 上次密码修改时间
     * @param passwordValidityDays   密码有效期(天)
     * @return 剩余天数，负数表示已过期
     */
    public static int getPasswordExpiryDays(long lastPasswordChangeTime, int passwordValidityDays) {
        if (passwordValidityDays <= 0) {
            return Integer.MAX_VALUE; // 永不过期
        }
        
        long currentTime = System.currentTimeMillis();
        long validityMillis = passwordValidityDays * 24L * 60L * 60L * 1000L;
        long expiryTime = lastPasswordChangeTime + validityMillis;
        
        long remainingMillis = expiryTime - currentTime;
        return (int) (remainingMillis / (24L * 60L * 60L * 1000L));
    }
}