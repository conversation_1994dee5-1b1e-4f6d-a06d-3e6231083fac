package com.kingdee.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE username = #{username} AND deleted = 0")
    User findByUsername(@Param("username") String username);

    /**
     * 根据邮箱查询用户
     *
     * @param email 邮箱
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE email = #{email} AND deleted = 0")
    User findByEmail(@Param("email") String email);

    /**
     * 根据手机号查询用户
     *
     * @param phone 手机号
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE phone = #{phone} AND deleted = 0")
    User findByPhone(@Param("phone") String phone);

    /**
     * 根据员工编号查询用户
     *
     * @param employeeNo 员工编号
     * @return 用户信息
     */
    @Select("SELECT * FROM sys_user WHERE employee_no = #{employeeNo} AND deleted = 0")
    User findByEmployeeNo(@Param("employeeNo") String employeeNo);

    /**
     * 分页查询用户列表（带部门信息）
     *
     * @param page 分页参数
     * @param username 用户名（模糊查询）
     * @param realName 真实姓名（模糊查询）
     * @param email 邮箱（模糊查询）
     * @param departmentId 部门ID
     * @param status 状态
     * @return 用户分页列表
     */
    IPage<User> selectUserPage(Page<User> page, 
                              @Param("username") String username,
                              @Param("realName") String realName,
                              @Param("email") String email,
                              @Param("departmentId") Long departmentId,
                              @Param("status") Integer status);

    /**
     * 根据角色ID查询用户列表
     *
     * @param roleId 角色ID
     * @return 用户列表
     */
    List<User> selectUsersByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据部门ID查询用户列表
     *
     * @param departmentId 部门ID
     * @param includeSubDept 是否包含子部门
     * @return 用户列表
     */
    List<User> selectUsersByDepartmentId(@Param("departmentId") Long departmentId, 
                                        @Param("includeSubDept") Boolean includeSubDept);

    /**
     * 根据权限编码查询用户列表
     *
     * @param permissionCode 权限编码
     * @return 用户列表
     */
    List<User> selectUsersByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 更新用户最后登录信息
     *
     * @param userId 用户ID
     * @param loginTime 登录时间
     * @param loginIp 登录IP
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET last_login_time = #{loginTime}, last_login_ip = #{loginIp}, " +
            "login_failure_count = 0 WHERE id = #{userId}")
    int updateLastLoginInfo(@Param("userId") Long userId, 
                           @Param("loginTime") LocalDateTime loginTime, 
                           @Param("loginIp") String loginIp);

    /**
     * 增加登录失败次数
     *
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET login_failure_count = COALESCE(login_failure_count, 0) + 1 " +
            "WHERE id = #{userId}")
    int incrementLoginFailureCount(@Param("userId") Long userId);

    /**
     * 锁定用户账户
     *
     * @param userId 用户ID
     * @param lockTime 锁定时间
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET locked = 1, lock_time = #{lockTime} WHERE id = #{userId}")
    int lockUser(@Param("userId") Long userId, @Param("lockTime") LocalDateTime lockTime);

    /**
     * 解锁用户账户
     *
     * @param userId 用户ID
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET locked = 0, lock_time = NULL, login_failure_count = 0 " +
            "WHERE id = #{userId}")
    int unlockUser(@Param("userId") Long userId);

    /**
     * 重置用户密码
     *
     * @param userId 用户ID
     * @param newPassword 新密码（已加密）
     * @param passwordExpireTime 密码过期时间
     * @return 更新行数
     */
    @Update("UPDATE sys_user SET password = #{newPassword}, password_expire_time = #{passwordExpireTime} " +
            "WHERE id = #{userId}")
    int resetPassword(@Param("userId") Long userId, 
                     @Param("newPassword") String newPassword, 
                     @Param("passwordExpireTime") LocalDateTime passwordExpireTime);

    /**
     * 统计用户数量
     *
     * @param status 状态（可选）
     * @param departmentId 部门ID（可选）
     * @return 用户数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM sys_user WHERE deleted = 0 " +
            "<if test='status != null'>AND status = #{status}</if> " +
            "<if test='departmentId != null'>AND department_id = #{departmentId}</if>" +
            "</script>")
    Long countUsers(@Param("status") Integer status, @Param("departmentId") Long departmentId);

    /**
     * 检查用户名是否存在
     *
     * @param username 用户名
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 FROM sys_user WHERE username = #{username} AND deleted = 0 " +
            "<if test='excludeUserId != null'>AND id != #{excludeUserId}</if>" +
            "</script>")
    Boolean existsByUsername(@Param("username") String username, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查邮箱是否存在
     *
     * @param email 邮箱
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 FROM sys_user WHERE email = #{email} AND deleted = 0 " +
            "<if test='excludeUserId != null'>AND id != #{excludeUserId}</if>" +
            "</script>")
    Boolean existsByEmail(@Param("email") String email, @Param("excludeUserId") Long excludeUserId);

    /**
     * 检查手机号是否存在
     *
     * @param phone 手机号
     * @param excludeUserId 排除的用户ID（用于更新时检查）
     * @return 是否存在
     */
    @Select("<script>" +
            "SELECT COUNT(*) > 0 FROM sys_user WHERE phone = #{phone} AND deleted = 0 " +
            "<if test='excludeUserId != null'>AND id != #{excludeUserId}</if>" +
            "</script>")
    Boolean existsByPhone(@Param("phone") String phone, @Param("excludeUserId") Long excludeUserId);
}