package com.kingdee.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 权限Mapper接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 根据权限编码查询权限
     *
     * @param permissionCode 权限编码
     * @return 权限信息
     */
    Permission findByPermissionCode(@Param("permissionCode") String permissionCode);

    /**
     * 根据权限名称查询权限
     *
     * @param permissionName 权限名称
     * @return 权限信息
     */
    Permission findByPermissionName(@Param("permissionName") String permissionName);

    /**
     * 分页查询权限列表
     *
     * @param page 分页参数
     * @param permissionCode 权限编码（模糊查询）
     * @param permissionName 权限名称（模糊查询）
     * @param permissionType 权限类型
     * @param status 状态
     * @param parentId 父权限ID
     * @return 权限分页列表
     */
    IPage<Permission> pagePermissions(Page<Permission> page,
                                    @Param("permissionCode") String permissionCode,
                                    @Param("permissionName") String permissionName,
                                    @Param("permissionType") String permissionType,
                                    @Param("status") Integer status,
                                    @Param("parentId") Long parentId);

    /**
     * 获取权限树结构
     *
     * @param parentId 父权限ID（null表示根节点）
     * @return 权限列表
     */
    List<Permission> getPermissionTree(@Param("parentId") Long parentId);

    /**
     * 获取所有启用的权限
     *
     * @return 权限列表
     */
    List<Permission> getEnabledPermissions();

    /**
     * 根据权限类型查询权限列表
     *
     * @param permissionType 权限类型
     * @return 权限列表
     */
    List<Permission> getPermissionsByType(@Param("permissionType") String permissionType);

    /**
     * 根据角色ID查询权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleId(@Param("roleId") Long roleId);

    /**
     * 根据用户ID查询权限列表
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    List<Permission> getPermissionsByUserId(@Param("userId") Long userId);

    /**
     * 获取菜单权限列表
     *
     * @return 菜单权限列表
     */
    List<Permission> getMenuPermissions();

    /**
     * 获取按钮权限列表
     *
     * @return 按钮权限列表
     */
    List<Permission> getButtonPermissions();

    /**
     * 获取API权限列表
     *
     * @return API权限列表
     */
    List<Permission> getApiPermissions();

    /**
     * 根据父权限ID查询子权限列表
     *
     * @param parentId 父权限ID
     * @return 子权限列表
     */
    List<Permission> getChildPermissions(@Param("parentId") Long parentId);

    /**
     * 获取权限路径
     *
     * @param permissionId 权限ID
     * @return 权限路径
     */
    String getPermissionPath(@Param("permissionId") Long permissionId);

    /**
     * 检查权限编码是否存在
     *
     * @param permissionCode 权限编码
     * @param excludePermissionId 排除的权限ID
     * @return 权限数量
     */
    int existsByPermissionCode(@Param("permissionCode") String permissionCode, 
                              @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 检查权限名称是否存在
     *
     * @param permissionName 权限名称
     * @param excludePermissionId 排除的权限ID
     * @return 权限数量
     */
    int existsByPermissionName(@Param("permissionName") String permissionName, 
                              @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 检查URL路径是否存在
     *
     * @param url URL路径
     * @param excludePermissionId 排除的权限ID
     * @return 权限数量
     */
    int existsByUrl(@Param("url") String url, 
                   @Param("excludePermissionId") Long excludePermissionId);

    /**
     * 统计权限数量
     *
     * @param status 状态
     * @param permissionType 权限类型
     * @return 权限数量
     */
    Long countPermissions(@Param("status") Integer status, 
                         @Param("permissionType") String permissionType);

    /**
     * 获取权限最大排序号
     *
     * @param parentId 父权限ID
     * @return 最大排序号
     */
    Integer getMaxSortOrder(@Param("parentId") Long parentId);

    /**
     * 更新权限状态
     *
     * @param permissionId 权限ID
     * @param status 状态
     * @return 更新数量
     */
    int updatePermissionStatus(@Param("permissionId") Long permissionId, 
                              @Param("status") Integer status);

    /**
     * 批量更新权限状态
     *
     * @param permissionIds 权限ID列表
     * @param status 状态
     * @return 更新数量
     */
    int batchUpdatePermissionStatus(@Param("permissionIds") List<Long> permissionIds, 
                                   @Param("status") Integer status);

    /**
     * 获取权限详情（包含父权限信息）
     *
     * @param permissionId 权限ID
     * @return 权限详情
     */
    Permission getPermissionWithParent(@Param("permissionId") Long permissionId);

    /**
     * 检查权限是否被角色使用
     *
     * @param permissionId 权限ID
     * @return 使用数量
     */
    int checkPermissionInUse(@Param("permissionId") Long permissionId);

    /**
     * 根据URL查询权限
     *
     * @param url URL路径
     * @param method HTTP方法
     * @return 权限列表
     */
    List<Permission> getPermissionsByUrl(@Param("url") String url, 
                                        @Param("method") String method);

    /**
     * 获取用户菜单权限树
     *
     * @param userId 用户ID
     * @return 菜单权限树
     */
    List<Permission> getUserMenuTree(@Param("userId") Long userId);

    /**
     * 获取用户按钮权限列表
     *
     * @param userId 用户ID
     * @return 按钮权限列表
     */
    List<String> getUserButtonPermissions(@Param("userId") Long userId);

    /**
     * 获取用户API权限列表
     *
     * @param userId 用户ID
     * @return API权限列表
     */
    List<String> getUserApiPermissions(@Param("userId") Long userId);

    /**
     * 根据角色ID列表查询权限
     *
     * @param roleIds 角色ID列表
     * @return 权限列表
     */
    List<Permission> getPermissionsByRoleIds(@Param("roleIds") List<Long> roleIds);

    /**
     * 获取系统权限列表
     *
     * @return 系统权限列表
     */
    List<Permission> getSystemPermissions();

    /**
     * 获取业务权限列表
     *
     * @return 业务权限列表
     */
    List<Permission> getBusinessPermissions();

    /**
     * 更新权限路径
     *
     * @param permissionId 权限ID
     * @param levelPath 权限路径
     * @return 更新数量
     */
    int updatePermissionPath(@Param("permissionId") Long permissionId, 
                            @Param("levelPath") String levelPath);

    /**
     * 批量更新子权限路径
     *
     * @param oldPath 旧路径前缀
     * @param newPath 新路径前缀
     * @return 更新数量
     */
    int batchUpdateChildPermissionPath(@Param("oldPath") String oldPath, 
                                      @Param("newPath") String newPath);
}