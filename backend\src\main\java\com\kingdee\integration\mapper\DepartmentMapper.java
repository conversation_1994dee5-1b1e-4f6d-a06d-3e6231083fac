package com.kingdee.integration.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.entity.Department;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门Mapper接口
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    /**
     * 根据部门编码查询部门
     * 
     * @param deptCode 部门编码
     * @return 部门信息
     */
    Department selectByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 根据部门名称查询部门
     * 
     * @param deptName 部门名称
     * @return 部门信息
     */
    Department selectByDeptName(@Param("deptName") String deptName);

    /**
     * 分页查询部门列表
     * 
     * @param page 分页参数
     * @param deptName 部门名称
     * @param deptCode 部门编码
     * @param deptType 部门类型
     * @param status 状态
     * @param parentId 父部门ID
     * @return 部门分页列表
     */
    IPage<Department> selectDepartmentPage(Page<Department> page,
                                         @Param("deptName") String deptName,
                                         @Param("deptCode") String deptCode,
                                         @Param("deptType") String deptType,
                                         @Param("status") Integer status,
                                         @Param("parentId") Long parentId);

    /**
     * 获取部门树结构
     * 
     * @return 部门树列表
     */
    List<Department> selectDepartmentTree();

    /**
     * 获取启用的部门列表
     * 
     * @return 启用的部门列表
     */
    List<Department> selectEnabledDepartments();

    /**
     * 根据部门类型查询部门列表
     * 
     * @param deptType 部门类型
     * @return 部门列表
     */
    List<Department> selectDepartmentsByType(@Param("deptType") String deptType);

    /**
     * 根据用户ID查询部门
     * 
     * @param userId 用户ID
     * @return 部门信息
     */
    Department selectDepartmentByUserId(@Param("userId") Long userId);

    /**
     * 根据负责人ID查询部门列表
     * 
     * @param managerId 负责人ID
     * @return 部门列表
     */
    List<Department> selectDepartmentsByManagerId(@Param("managerId") Long managerId);

    /**
     * 获取子部门列表
     * 
     * @param parentId 父部门ID
     * @return 子部门列表
     */
    List<Department> selectChildDepartments(@Param("parentId") Long parentId);

    /**
     * 获取所有子部门ID（包括子子部门）
     * 
     * @param parentId 父部门ID
     * @return 子部门ID列表
     */
    List<Long> selectAllChildDepartmentIds(@Param("parentId") Long parentId);

    /**
     * 获取部门路径
     * 
     * @param deptId 部门ID
     * @return 部门路径
     */
    String selectDepartmentPath(@Param("deptId") Long deptId);

    /**
     * 检查部门编码是否存在
     * 
     * @param deptCode 部门编码
     * @return 是否存在
     */
    boolean existsByDeptCode(@Param("deptCode") String deptCode);

    /**
     * 检查部门名称是否存在
     * 
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 是否存在
     */
    boolean existsByDeptName(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    /**
     * 统计部门数量
     * 
     * @return 部门总数
     */
    Long countDepartments();

    /**
     * 根据类型统计部门数量
     * 
     * @param deptType 部门类型
     * @return 部门数量
     */
    Long countDepartmentsByType(@Param("deptType") String deptType);

    /**
     * 根据状态统计部门数量
     * 
     * @param status 状态
     * @return 部门数量
     */
    Long countDepartmentsByStatus(@Param("status") Integer status);

    /**
     * 获取最大排序号
     * 
     * @param parentId 父部门ID
     * @return 最大排序号
     */
    Integer selectMaxSortOrder(@Param("parentId") Long parentId);

    /**
     * 更新部门状态
     * 
     * @param deptId 部门ID
     * @param status 状态
     * @return 影响行数
     */
    int updateDepartmentStatus(@Param("deptId") Long deptId, @Param("status") Integer status);

    /**
     * 批量更新部门状态
     * 
     * @param deptIds 部门ID列表
     * @param status 状态
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("deptIds") List<Long> deptIds, @Param("status") Integer status);

    /**
     * 获取部门详情（包含父部门信息）
     * 
     * @param deptId 部门ID
     * @return 部门详情
     */
    Department selectDepartmentWithParent(@Param("deptId") Long deptId);

    /**
     * 检查部门是否有子部门
     * 
     * @param deptId 部门ID
     * @return 是否有子部门
     */
    boolean hasChildDepartments(@Param("deptId") Long deptId);

    /**
     * 检查部门是否有用户
     * 
     * @param deptId 部门ID
     * @return 是否有用户
     */
    boolean hasUsers(@Param("deptId") Long deptId);

    /**
     * 获取部门用户数量
     * 
     * @param deptId 部门ID
     * @return 用户数量
     */
    Long getDepartmentUserCount(@Param("deptId") Long deptId);

    /**
     * 更新部门路径
     * 
     * @param deptId 部门ID
     * @param deptPath 部门路径
     * @return 影响行数
     */
    int updateDepartmentPath(@Param("deptId") Long deptId, @Param("deptPath") String deptPath);

    /**
     * 批量更新子部门路径
     * 
     * @param parentId 父部门ID
     * @param oldPath 旧路径前缀
     * @param newPath 新路径前缀
     * @return 影响行数
     */
    int batchUpdateChildDepartmentPaths(@Param("parentId") Long parentId,
                                       @Param("oldPath") String oldPath,
                                       @Param("newPath") String newPath);

    /**
     * 更新部门层级
     * 
     * @param deptId 部门ID
     * @param deptLevel 部门层级
     * @return 影响行数
     */
    int updateDepartmentLevel(@Param("deptId") Long deptId, @Param("deptLevel") Integer deptLevel);

    /**
     * 批量更新子部门层级
     * 
     * @param parentId 父部门ID
     * @param levelIncrement 层级增量
     * @return 影响行数
     */
    int batchUpdateChildDepartmentLevels(@Param("parentId") Long parentId,
                                        @Param("levelIncrement") Integer levelIncrement);

    /**
     * 根据部门路径查询部门列表
     * 
     * @param pathPrefix 路径前缀
     * @return 部门列表
     */
    List<Department> selectDepartmentsByPathPrefix(@Param("pathPrefix") String pathPrefix);

    /**
     * 获取根部门列表
     * 
     * @return 根部门列表
     */
    List<Department> selectRootDepartments();

    /**
     * 获取叶子部门列表
     * 
     * @return 叶子部门列表
     */
    List<Department> selectLeafDepartments();

    /**
     * 根据层级查询部门列表
     * 
     * @param deptLevel 部门层级
     * @return 部门列表
     */
    List<Department> selectDepartmentsByLevel(@Param("deptLevel") Integer deptLevel);

    /**
     * 获取部门层级结构
     * 
     * @param maxLevel 最大层级
     * @return 部门层级列表
     */
    List<Department> selectDepartmentHierarchy(@Param("maxLevel") Integer maxLevel);

    /**
     * 搜索部门（支持模糊查询）
     * 
     * @param keyword 关键词
     * @return 部门列表
     */
    List<Department> searchDepartments(@Param("keyword") String keyword);

    /**
     * 获取用户可访问的部门列表
     * 
     * @param userId 用户ID
     * @return 部门列表
     */
    List<Department> selectAccessibleDepartments(@Param("userId") Long userId);

    /**
     * 获取部门统计信息
     * 
     * @return 统计信息Map
     */
    List<java.util.Map<String, Object>> selectDepartmentStatistics();
}