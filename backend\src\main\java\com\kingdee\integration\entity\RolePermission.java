package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kingdee.integration.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * 角色权限关联实体类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_role_permission")
public class RolePermission extends BaseEntity {

    /**
     * 角色ID
     */
    @NotNull(message = "角色ID不能为空")
    @TableField("role_id")
    private Long roleId;

    /**
     * 权限ID
     */
    @NotNull(message = "权限ID不能为空")
    @TableField("permission_id")
    private Long permissionId;

    /**
     * 权限类型（1：授予，2：拒绝）
     */
    @TableField("grant_type")
    private Integer grantType;

    /**
     * 数据权限范围（JSON格式，存储具体的数据权限配置）
     */
    @TableField("data_scope")
    private String dataScope;

    /**
     * 状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    // 非数据库字段
    
    /**
     * 角色编码
     */
    @TableField(exist = false)
    private String roleCode;

    /**
     * 角色名称
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 权限编码
     */
    @TableField(exist = false)
    private String permissionCode;

    /**
     * 权限名称
     */
    @TableField(exist = false)
    private String permissionName;

    /**
     * 权限类型
     */
    @TableField(exist = false)
    private Integer permissionType;

    /**
     * 构造函数
     */
    public RolePermission() {}

    public RolePermission(Long roleId, Long permissionId) {
        this.roleId = roleId;
        this.permissionId = permissionId;
        this.grantType = 1; // 默认授予
        this.status = 1; // 默认启用
    }

    public RolePermission(Long roleId, Long permissionId, Integer grantType) {
        this.roleId = roleId;
        this.permissionId = permissionId;
        this.grantType = grantType;
        this.status = 1; // 默认启用
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 是否授予权限
     */
    public boolean isGranted() {
        return this.grantType != null && this.grantType == 1;
    }

    /**
     * 是否拒绝权限
     */
    public boolean isDenied() {
        return this.grantType != null && this.grantType == 2;
    }
}