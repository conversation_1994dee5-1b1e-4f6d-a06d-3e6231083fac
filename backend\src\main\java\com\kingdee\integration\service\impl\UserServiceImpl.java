package com.kingdee.integration.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.kingdee.integration.common.exception.BusinessException;
import com.kingdee.integration.common.result.ResultCode;
import com.kingdee.integration.entity.User;
import com.kingdee.integration.mapper.UserMapper;
import com.kingdee.integration.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    private final PasswordEncoder passwordEncoder;

    @Override
    public User findByUsername(String username) {
        if (!StringUtils.hasText(username)) {
            return null;
        }
        return baseMapper.findByUsername(username);
    }

    @Override
    public User findByEmail(String email) {
        if (!StringUtils.hasText(email)) {
            return null;
        }
        return baseMapper.findByEmail(email);
    }

    @Override
    public User findByPhone(String phone) {
        if (!StringUtils.hasText(phone)) {
            return null;
        }
        return baseMapper.findByPhone(phone);
    }

    @Override
    public User findByEmployeeNo(String employeeNo) {
        if (!StringUtils.hasText(employeeNo)) {
            return null;
        }
        return baseMapper.findByEmployeeNo(employeeNo);
    }

    @Override
    public IPage<User> pageUsers(Page<User> page, String username, String realName, 
                                String email, Long departmentId, Integer status) {
        return baseMapper.pageUsers(page, username, realName, email, departmentId, status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User createUser(User user) {
        // 参数验证
        validateUserForCreate(user);
        
        // 检查用户名是否已存在
        if (existsByUsername(user.getUsername(), null)) {
            throw new BusinessException(ResultCode.USER_USERNAME_EXISTS);
        }
        
        // 检查邮箱是否已存在
        if (StringUtils.hasText(user.getEmail()) && existsByEmail(user.getEmail(), null)) {
            throw new BusinessException(ResultCode.USER_EMAIL_EXISTS);
        }
        
        // 检查手机号是否已存在
        if (StringUtils.hasText(user.getPhone()) && existsByPhone(user.getPhone(), null)) {
            throw new BusinessException(ResultCode.USER_PHONE_EXISTS);
        }
        
        // 加密密码
        if (StringUtils.hasText(user.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        }
        
        // 设置默认值
        if (user.getStatus() == null) {
            user.setStatus(1); // 默认启用
        }
        if (user.getAccountLocked() == null) {
            user.setAccountLocked(false);
        }
        if (user.getLoginFailureCount() == null) {
            user.setLoginFailureCount(0);
        }
        
        // 保存用户
        boolean saved = save(user);
        if (!saved) {
            throw new BusinessException(ResultCode.USER_CREATE_FAILED);
        }
        
        log.info("用户创建成功: {}", user.getUsername());
        return user;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public User updateUser(User user) {
        // 参数验证
        BusinessException.notNull(user, "用户信息不能为空");
        BusinessException.notNull(user.getId(), "用户ID不能为空");
        
        // 检查用户是否存在
        User existingUser = getById(user.getId());
        BusinessException.notNull(existingUser, "用户不存在");
        
        // 检查用户名是否已被其他用户使用
        if (StringUtils.hasText(user.getUsername()) && 
            !user.getUsername().equals(existingUser.getUsername()) &&
            existsByUsername(user.getUsername(), user.getId())) {
            throw new BusinessException(ResultCode.USER_USERNAME_EXISTS);
        }
        
        // 检查邮箱是否已被其他用户使用
        if (StringUtils.hasText(user.getEmail()) && 
            !Objects.equals(user.getEmail(), existingUser.getEmail()) &&
            existsByEmail(user.getEmail(), user.getId())) {
            throw new BusinessException(ResultCode.USER_EMAIL_EXISTS);
        }
        
        // 检查手机号是否已被其他用户使用
        if (StringUtils.hasText(user.getPhone()) && 
            !Objects.equals(user.getPhone(), existingUser.getPhone()) &&
            existsByPhone(user.getPhone(), user.getId())) {
            throw new BusinessException(ResultCode.USER_PHONE_EXISTS);
        }
        
        // 如果密码有变更，需要加密
        if (StringUtils.hasText(user.getPassword()) && 
            !user.getPassword().equals(existingUser.getPassword())) {
            user.setPassword(passwordEncoder.encode(user.getPassword()));
        } else {
            user.setPassword(null); // 不更新密码
        }
        
        // 更新用户
        boolean updated = updateById(user);
        if (!updated) {
            throw new BusinessException(ResultCode.USER_UPDATE_FAILED);
        }
        
        log.info("用户更新成功: {}", user.getUsername());
        return getById(user.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUser(Long userId) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        // 检查是否为系统管理员
        if (user.isAdmin()) {
            throw new BusinessException(ResultCode.USER_ADMIN_CANNOT_DELETE);
        }
        
        boolean deleted = removeById(userId);
        if (deleted) {
            log.info("用户删除成功: {}", user.getUsername());
        }
        
        return deleted;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return true;
        }
        
        // 检查是否包含系统管理员
        List<User> users = listByIds(userIds);
        for (User user : users) {
            if (user.isAdmin()) {
                throw new BusinessException(ResultCode.USER_ADMIN_CANNOT_DELETE);
            }
        }
        
        boolean deleted = removeByIds(userIds);
        if (deleted) {
            log.info("批量删除用户成功，数量: {}", userIds.size());
        }
        
        return deleted;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableUser(Long userId) {
        return updateUserStatus(userId, 1, "启用");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableUser(Long userId) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        // 检查是否为系统管理员
        if (user.isAdmin()) {
            throw new BusinessException(ResultCode.USER_ADMIN_CANNOT_DISABLE);
        }
        
        return updateUserStatus(userId, 0, "禁用");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean lockUser(Long userId) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        // 检查是否为系统管理员
        if (user.isAdmin()) {
            throw new BusinessException(ResultCode.USER_ADMIN_CANNOT_LOCK);
        }
        
        return baseMapper.lockUser(userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean unlockUser(Long userId) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        return baseMapper.unlockUser(userId) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean resetPassword(Long userId, String newPassword) {
        BusinessException.notNull(userId, "用户ID不能为空");
        BusinessException.hasText(newPassword, "新密码不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        String encodedPassword = passwordEncoder.encode(newPassword);
        boolean updated = baseMapper.resetPassword(userId, encodedPassword) > 0;
        
        if (updated) {
            log.info("用户密码重置成功: {}", user.getUsername());
        }
        
        return updated;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        BusinessException.notNull(userId, "用户ID不能为空");
        BusinessException.hasText(oldPassword, "旧密码不能为空");
        BusinessException.hasText(newPassword, "新密码不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        // 验证旧密码
        if (!validatePassword(user, oldPassword)) {
            throw new BusinessException(ResultCode.USER_OLD_PASSWORD_ERROR);
        }
        
        // 更新密码
        String encodedPassword = passwordEncoder.encode(newPassword);
        boolean updated = baseMapper.resetPassword(userId, encodedPassword) > 0;
        
        if (updated) {
            log.info("用户密码修改成功: {}", user.getUsername());
        }
        
        return updated;
    }

    @Override
    public boolean validatePassword(User user, String rawPassword) {
        if (user == null || !StringUtils.hasText(rawPassword) || !StringUtils.hasText(user.getPassword())) {
            return false;
        }
        return passwordEncoder.matches(rawPassword, user.getPassword());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLastLoginInfo(Long userId, String loginIp) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        return baseMapper.updateLastLoginInfo(userId, LocalDateTime.now(), loginIp) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementLoginFailureCount(Long userId) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        return baseMapper.incrementLoginFailureCount(userId) > 0;
    }

    @Override
    public List<User> getUsersByRoleId(Long roleId) {
        if (roleId == null) {
            return new ArrayList<>();
        }
        return baseMapper.getUsersByRoleId(roleId);
    }

    @Override
    public List<User> getUsersByDepartmentId(Long departmentId, Boolean includeSubDept) {
        if (departmentId == null) {
            return new ArrayList<>();
        }
        return baseMapper.getUsersByDepartmentId(departmentId, includeSubDept);
    }

    @Override
    public List<User> getUsersByPermissionCode(String permissionCode) {
        if (!StringUtils.hasText(permissionCode)) {
            return new ArrayList<>();
        }
        return baseMapper.getUsersByPermissionCode(permissionCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean assignRoles(Long userId, List<Long> roleIds) {
        // TODO: 实现用户角色分配逻辑
        // 这里需要操作用户角色关联表
        log.info("分配用户角色: userId={}, roleIds={}", userId, roleIds);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean removeRoles(Long userId, List<Long> roleIds) {
        // TODO: 实现用户角色移除逻辑
        // 这里需要操作用户角色关联表
        log.info("移除用户角色: userId={}, roleIds={}", userId, roleIds);
        return true;
    }

    @Override
    public List<String> getUserRoles(Long userId) {
        // TODO: 实现获取用户角色逻辑
        // 这里需要查询用户角色关联表
        return new ArrayList<>();
    }

    @Override
    public List<String> getUserPermissions(Long userId) {
        // TODO: 实现获取用户权限逻辑
        // 这里需要通过角色查询权限
        return new ArrayList<>();
    }

    @Override
    public boolean hasPermission(Long userId, String permissionCode) {
        List<String> permissions = getUserPermissions(userId);
        return permissions.contains(permissionCode);
    }

    @Override
    public boolean hasRole(Long userId, String roleCode) {
        List<String> roles = getUserRoles(userId);
        return roles.contains(roleCode);
    }

    @Override
    public boolean existsByUsername(String username, Long excludeUserId) {
        if (!StringUtils.hasText(username)) {
            return false;
        }
        return baseMapper.existsByUsername(username, excludeUserId) > 0;
    }

    @Override
    public boolean existsByEmail(String email, Long excludeUserId) {
        if (!StringUtils.hasText(email)) {
            return false;
        }
        return baseMapper.existsByEmail(email, excludeUserId) > 0;
    }

    @Override
    public boolean existsByPhone(String phone, Long excludeUserId) {
        if (!StringUtils.hasText(phone)) {
            return false;
        }
        return baseMapper.existsByPhone(phone, excludeUserId) > 0;
    }

    @Override
    public Long countUsers(Integer status, Long departmentId) {
        return baseMapper.countUsers(status, departmentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResult importUsers(List<User> users, boolean updateExisting) {
        if (CollectionUtils.isEmpty(users)) {
            return new ImportResult(0, 0, 0, new ArrayList<>());
        }
        
        int totalCount = users.size();
        int successCount = 0;
        int failureCount = 0;
        List<String> errorMessages = new ArrayList<>();
        
        for (User user : users) {
            try {
                // 检查用户是否已存在
                User existingUser = findByUsername(user.getUsername());
                
                if (existingUser != null) {
                    if (updateExisting) {
                        user.setId(existingUser.getId());
                        updateUser(user);
                        successCount++;
                    } else {
                        errorMessages.add(String.format("用户 %s 已存在", user.getUsername()));
                        failureCount++;
                    }
                } else {
                    createUser(user);
                    successCount++;
                }
            } catch (Exception e) {
                errorMessages.add(String.format("用户 %s 导入失败: %s", user.getUsername(), e.getMessage()));
                failureCount++;
                log.error("用户导入失败", e);
            }
        }
        
        log.info("用户导入完成: 总数={}, 成功={}, 失败={}", totalCount, successCount, failureCount);
        return new ImportResult(totalCount, successCount, failureCount, errorMessages);
    }

    @Override
    public List<User> exportUsers(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return list();
        }
        return listByIds(userIds);
    }

    /**
     * 验证用户创建参数
     */
    private void validateUserForCreate(User user) {
        BusinessException.notNull(user, "用户信息不能为空");
        BusinessException.hasText(user.getUsername(), "用户名不能为空");
        BusinessException.hasText(user.getRealName(), "真实姓名不能为空");
    }

    /**
     * 更新用户状态
     */
    private boolean updateUserStatus(Long userId, Integer status, String operation) {
        BusinessException.notNull(userId, "用户ID不能为空");
        
        User user = getById(userId);
        BusinessException.notNull(user, "用户不存在");
        
        LambdaUpdateWrapper<User> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(User::getId, userId)
                    .set(User::getStatus, status);
        
        boolean updated = update(updateWrapper);
        if (updated) {
            log.info("用户{}成功: {}", operation, user.getUsername());
        }
        
        return updated;
    }
}