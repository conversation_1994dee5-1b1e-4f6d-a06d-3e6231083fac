package com.kingdee.integration.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.kingdee.integration.common.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 权限实体类
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_permission")
public class Permission extends BaseEntity {

    /**
     * 权限编码
     */
    @NotBlank(message = "权限编码不能为空")
    @Size(max = 100, message = "权限编码长度不能超过100个字符")
    @TableField("permission_code")
    private String permissionCode;

    /**
     * 权限名称
     */
    @NotBlank(message = "权限名称不能为空")
    @Size(max = 100, message = "权限名称长度不能超过100个字符")
    @TableField("permission_name")
    private String permissionName;

    /**
     * 权限描述
     */
    @Size(max = 500, message = "权限描述长度不能超过500个字符")
    @TableField("description")
    private String description;

    /**
     * 权限类型（1：菜单，2：按钮，3：接口）
     */
    @TableField("permission_type")
    private Integer permissionType;

    /**
     * 父权限ID
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 权限路径
     */
    @Size(max = 500, message = "权限路径长度不能超过500个字符")
    @TableField("path")
    private String path;

    /**
     * 组件路径
     */
    @Size(max = 500, message = "组件路径长度不能超过500个字符")
    @TableField("component")
    private String component;

    /**
     * 图标
     */
    @Size(max = 100, message = "图标长度不能超过100个字符")
    @TableField("icon")
    private String icon;

    /**
     * 排序号
     */
    @TableField("sort_order")
    private Integer sortOrder;

    /**
     * 权限状态（0：禁用，1：启用）
     */
    @TableField("status")
    private Integer status;

    /**
     * 是否显示（0：隐藏，1：显示）
     */
    @TableField("visible")
    private Integer visible;

    /**
     * 是否缓存（0：不缓存，1：缓存）
     */
    @TableField("keep_alive")
    private Integer keepAlive;

    /**
     * 是否外链（0：否，1：是）
     */
    @TableField("is_external")
    private Integer isExternal;

    /**
     * HTTP方法（GET,POST,PUT,DELETE等）
     */
    @TableField("http_method")
    private String httpMethod;

    /**
     * API路径
     */
    @Size(max = 500, message = "API路径长度不能超过500个字符")
    @TableField("api_path")
    private String apiPath;

    /**
     * 权限级别
     */
    @TableField("level")
    private Integer level;

    /**
     * 权限树路径
     */
    @TableField("tree_path")
    private String treePath;

    /**
     * 是否内置权限（0：否，1：是）
     */
    @TableField("is_builtin")
    private Integer isBuiltin;

    /**
     * 扩展属性（JSON格式）
     */
    @TableField("extra_data")
    private String extraData;

    // 非数据库字段
    
    /**
     * 父权限名称
     */
    @TableField(exist = false)
    private String parentName;

    /**
     * 子权限列表
     */
    @TableField(exist = false)
    private List<Permission> children;

    /**
     * 是否选中（用于权限分配）
     */
    @TableField(exist = false)
    private Boolean checked;

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return this.status != null && this.status == 1;
    }

    /**
     * 是否显示
     */
    public boolean isVisible() {
        return this.visible != null && this.visible == 1;
    }

    /**
     * 是否菜单权限
     */
    public boolean isMenu() {
        return this.permissionType != null && this.permissionType == 1;
    }

    /**
     * 是否按钮权限
     */
    public boolean isButton() {
        return this.permissionType != null && this.permissionType == 2;
    }

    /**
     * 是否接口权限
     */
    public boolean isApi() {
        return this.permissionType != null && this.permissionType == 3;
    }

    /**
     * 是否根权限
     */
    public boolean isRoot() {
        return this.parentId == null || this.parentId == 0;
    }

    /**
     * 是否内置权限
     */
    public boolean isBuiltinPermission() {
        return this.isBuiltin != null && this.isBuiltin == 1;
    }
}