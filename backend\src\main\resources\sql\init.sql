-- 金蝶星辰系统集成项目数据库初始化脚本
-- 创建时间: 2024-01-01
-- 版本: 1.0.0
-- 描述: 创建用户、角色、权限、部门等基础表结构

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ============================
-- 1. 部门表
-- ============================
DROP TABLE IF EXISTS `sys_department`;
CREATE TABLE `sys_department` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门ID，0表示根部门',
  `department_code` varchar(50) NOT NULL COMMENT '部门编码',
  `department_name` varchar(100) NOT NULL COMMENT '部门名称',
  `department_type` varchar(20) DEFAULT 'NORMAL' COMMENT '部门类型：COMPANY-公司,DEPARTMENT-部门,GROUP-小组',
  `level` int(11) DEFAULT '1' COMMENT '部门层级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `leader_id` bigint(20) DEFAULT NULL COMMENT '部门负责人ID',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `address` varchar(200) DEFAULT NULL COMMENT '地址',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_department_code` (`department_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='部门表';

-- ============================
-- 2. 用户表
-- ============================
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) NOT NULL COMMENT '真实姓名',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `employee_no` varchar(50) DEFAULT NULL COMMENT '工号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` tinyint(1) DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `avatar` varchar(200) DEFAULT NULL COMMENT '头像',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `department_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
  `position` varchar(100) DEFAULT NULL COMMENT '职位',
  `entry_date` date DEFAULT NULL COMMENT '入职日期',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_locked` tinyint(1) DEFAULT '0' COMMENT '是否锁定：0-否，1-是',
  `lock_time` datetime DEFAULT NULL COMMENT '锁定时间',
  `login_failure_count` int(11) DEFAULT '0' COMMENT '登录失败次数',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(50) DEFAULT NULL COMMENT '最后登录IP',
  `password_update_time` datetime DEFAULT NULL COMMENT '密码更新时间',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`),
  UNIQUE KEY `uk_phone` (`phone`),
  UNIQUE KEY `uk_employee_no` (`employee_no`),
  KEY `idx_department_id` (`department_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_last_login_time` (`last_login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- ============================
-- 3. 角色表
-- ============================
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_code` varchar(50) NOT NULL COMMENT '角色编码',
  `role_name` varchar(100) NOT NULL COMMENT '角色名称',
  `role_type` varchar(20) DEFAULT 'CUSTOM' COMMENT '角色类型：SYSTEM-系统角色,CUSTOM-自定义角色',
  `data_scope` varchar(20) DEFAULT 'SELF' COMMENT '数据权限：ALL-全部,DEPT-部门,DEPT_AND_SUB-部门及子部门,SELF-仅自己',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_code` (`role_code`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- ============================
-- 4. 权限表
-- ============================
DROP TABLE IF EXISTS `sys_permission`;
CREATE TABLE `sys_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `parent_id` bigint(20) DEFAULT '0' COMMENT '父权限ID，0表示根权限',
  `permission_code` varchar(100) NOT NULL COMMENT '权限编码',
  `permission_name` varchar(100) NOT NULL COMMENT '权限名称',
  `permission_type` varchar(20) DEFAULT 'MENU' COMMENT '权限类型：MENU-菜单,BUTTON-按钮,API-接口',
  `menu_url` varchar(200) DEFAULT NULL COMMENT '菜单URL',
  `menu_icon` varchar(100) DEFAULT NULL COMMENT '菜单图标',
  `api_url` varchar(200) DEFAULT NULL COMMENT 'API地址',
  `api_method` varchar(10) DEFAULT NULL COMMENT 'API方法：GET,POST,PUT,DELETE',
  `component` varchar(200) DEFAULT NULL COMMENT '组件路径',
  `level` int(11) DEFAULT '1' COMMENT '权限层级',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `is_external` tinyint(1) DEFAULT '0' COMMENT '是否外部链接：0-否，1-是',
  `is_cache` tinyint(1) DEFAULT '1' COMMENT '是否缓存：0-否，1-是',
  `is_visible` tinyint(1) DEFAULT '1' COMMENT '是否可见：0-否，1-是',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permission_code` (`permission_code`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_permission_type` (`permission_type`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- ============================
-- 5. 用户角色关联表
-- ============================
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `assign_by` bigint(20) DEFAULT NULL COMMENT '分配人',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活：0-否，1-是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- ============================
-- 6. 角色权限关联表
-- ============================
DROP TABLE IF EXISTS `sys_role_permission`;
CREATE TABLE `sys_role_permission` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `role_id` bigint(20) NOT NULL COMMENT '角色ID',
  `permission_id` bigint(20) NOT NULL COMMENT '权限ID',
  `assign_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `assign_by` bigint(20) DEFAULT NULL COMMENT '分配人',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活：0-否，1-是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permission` (`role_id`,`permission_id`),
  KEY `idx_role_id` (`role_id`),
  KEY `idx_permission_id` (`permission_id`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- ============================
-- 7. 系统配置表
-- ============================
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'STRING' COMMENT '配置类型：STRING-字符串,NUMBER-数字,BOOLEAN-布尔,JSON-JSON对象',
  `config_group` varchar(50) DEFAULT 'DEFAULT' COMMENT '配置分组',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `description` varchar(500) DEFAULT NULL COMMENT '描述',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统配置：0-否，1-是',
  `is_encrypted` tinyint(1) DEFAULT '0' COMMENT '是否加密：0-否，1-是',
  `sort_order` int(11) DEFAULT '0' COMMENT '排序号',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `create_by` bigint(20) DEFAULT NULL COMMENT '创建人',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` bigint(20) DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) DEFAULT '1' COMMENT '版本号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`),
  KEY `idx_config_group` (`config_group`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- ============================
-- 8. 操作日志表
-- ============================
DROP TABLE IF EXISTS `sys_operation_log`;
CREATE TABLE `sys_operation_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `trace_id` varchar(64) DEFAULT NULL COMMENT '链路追踪ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `operation_type` varchar(20) DEFAULT NULL COMMENT '操作类型：CREATE-创建,UPDATE-更新,DELETE-删除,QUERY-查询,LOGIN-登录,LOGOUT-登出',
  `module` varchar(50) DEFAULT NULL COMMENT '操作模块',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `method` varchar(200) DEFAULT NULL COMMENT '操作方法',
  `request_method` varchar(10) DEFAULT NULL COMMENT '请求方式',
  `request_url` varchar(500) DEFAULT NULL COMMENT '请求URL',
  `request_params` text COMMENT '请求参数',
  `response_result` text COMMENT '响应结果',
  `operation_ip` varchar(50) DEFAULT NULL COMMENT '操作IP',
  `operation_location` varchar(200) DEFAULT NULL COMMENT '操作地点',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `execution_time` bigint(20) DEFAULT NULL COMMENT '执行时间(毫秒)',
  `status` tinyint(1) DEFAULT '1' COMMENT '操作状态：0-失败，1-成功',
  `error_message` text COMMENT '错误信息',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_module` (`module`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_trace_id` (`trace_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='操作日志表';

-- ============================
-- 9. 登录日志表
-- ============================
DROP TABLE IF EXISTS `sys_login_log`;
CREATE TABLE `sys_login_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '用户名',
  `login_type` varchar(20) DEFAULT 'PASSWORD' COMMENT '登录类型：PASSWORD-密码,SMS-短信,EMAIL-邮箱,WECHAT-微信,QQ-QQ',
  `login_ip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `login_location` varchar(200) DEFAULT NULL COMMENT '登录地点',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `device_type` varchar(20) DEFAULT NULL COMMENT '设备类型：PC-电脑,MOBILE-手机,TABLET-平板',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `logout_time` datetime DEFAULT NULL COMMENT '登出时间',
  `session_duration` bigint(20) DEFAULT NULL COMMENT '会话时长(秒)',
  `status` tinyint(1) DEFAULT '1' COMMENT '登录状态：0-失败，1-成功',
  `failure_reason` varchar(200) DEFAULT NULL COMMENT '失败原因',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_username` (`username`),
  KEY `idx_login_type` (`login_type`),
  KEY `idx_login_ip` (`login_ip`),
  KEY `idx_status` (`status`),
  KEY `idx_login_time` (`login_time`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- ============================
-- 10. 文件信息表
-- ============================
DROP TABLE IF EXISTS `sys_file`;
CREATE TABLE `sys_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `file_name` varchar(200) NOT NULL COMMENT '文件名',
  `original_name` varchar(200) NOT NULL COMMENT '原始文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_url` varchar(500) DEFAULT NULL COMMENT '文件访问URL',
  `file_size` bigint(20) DEFAULT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(50) DEFAULT NULL COMMENT '文件类型',
  `file_extension` varchar(20) DEFAULT NULL COMMENT '文件扩展名',
  `mime_type` varchar(100) DEFAULT NULL COMMENT 'MIME类型',
  `md5_hash` varchar(64) DEFAULT NULL COMMENT 'MD5哈希值',
  `sha1_hash` varchar(64) DEFAULT NULL COMMENT 'SHA1哈希值',
  `storage_type` varchar(20) DEFAULT 'LOCAL' COMMENT '存储类型：LOCAL-本地,OSS-阿里云,COS-腾讯云,QINIU-七牛云',
  `bucket_name` varchar(100) DEFAULT NULL COMMENT '存储桶名称',
  `upload_by` bigint(20) DEFAULT NULL COMMENT '上传人',
  `business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `business_id` varchar(100) DEFAULT NULL COMMENT '业务ID',
  `is_temp` tinyint(1) DEFAULT '0' COMMENT '是否临时文件：0-否，1-是',
  `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
  `download_count` int(11) DEFAULT '0' COMMENT '下载次数',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
  `is_deleted` tinyint(1) DEFAULT '0' COMMENT '是否删除：0-否，1-是',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_file_path` (`file_path`),
  KEY `idx_file_name` (`file_name`),
  KEY `idx_file_type` (`file_type`),
  KEY `idx_upload_by` (`upload_by`),
  KEY `idx_business_type` (`business_type`),
  KEY `idx_business_id` (`business_id`),
  KEY `idx_md5_hash` (`md5_hash`),
  KEY `idx_status` (`status`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件信息表';

-- ============================
-- 初始化数据
-- ============================

-- 初始化部门数据
INSERT INTO `sys_department` (`id`, `parent_id`, `department_code`, `department_name`, `department_type`, `level`, `sort_order`, `description`, `status`, `create_by`, `create_time`) VALUES
(1, 0, 'ROOT', '根部门', 'COMPANY', 1, 0, '系统根部门', 1, 1, NOW()),
(2, 1, 'TECH', '技术部', 'DEPARTMENT', 2, 1, '技术研发部门', 1, 1, NOW()),
(3, 1, 'SALES', '销售部', 'DEPARTMENT', 2, 2, '销售业务部门', 1, 1, NOW()),
(4, 1, 'HR', '人事部', 'DEPARTMENT', 2, 3, '人力资源部门', 1, 1, NOW()),
(5, 1, 'FINANCE', '财务部', 'DEPARTMENT', 2, 4, '财务管理部门', 1, 1, NOW()),
(6, 2, 'DEV', '开发组', 'GROUP', 3, 1, '软件开发小组', 1, 1, NOW()),
(7, 2, 'TEST', '测试组', 'GROUP', 3, 2, '软件测试小组', 1, 1, NOW());

-- 初始化角色数据
INSERT INTO `sys_role` (`id`, `role_code`, `role_name`, `role_type`, `data_scope`, `description`, `sort_order`, `status`, `create_by`, `create_time`) VALUES
(1, 'SUPER_ADMIN', '超级管理员', 'SYSTEM', 'ALL', '系统超级管理员，拥有所有权限', 1, 1, 1, NOW()),
(2, 'ADMIN', '系统管理员', 'SYSTEM', 'ALL', '系统管理员，拥有大部分权限', 2, 1, 1, NOW()),
(3, 'DEPT_ADMIN', '部门管理员', 'CUSTOM', 'DEPT_AND_SUB', '部门管理员，管理本部门及子部门', 3, 1, 1, NOW()),
(4, 'USER', '普通用户', 'CUSTOM', 'SELF', '普通用户，只能查看自己的数据', 4, 1, 1, NOW()),
(5, 'GUEST', '访客', 'CUSTOM', 'SELF', '访客用户，只有基本查看权限', 5, 1, 1, NOW());

-- 初始化权限数据
INSERT INTO `sys_permission` (`id`, `parent_id`, `permission_code`, `permission_name`, `permission_type`, `menu_url`, `menu_icon`, `level`, `sort_order`, `description`, `status`, `create_by`, `create_time`) VALUES
-- 系统管理
(1, 0, 'SYSTEM', '系统管理', 'MENU', '/system', 'system', 1, 1, '系统管理模块', 1, 1, NOW()),
(2, 1, 'SYSTEM:USER', '用户管理', 'MENU', '/system/user', 'user', 2, 1, '用户管理页面', 1, 1, NOW()),
(3, 1, 'SYSTEM:ROLE', '角色管理', 'MENU', '/system/role', 'role', 2, 2, '角色管理页面', 1, 1, NOW()),
(4, 1, 'SYSTEM:PERMISSION', '权限管理', 'MENU', '/system/permission', 'permission', 2, 3, '权限管理页面', 1, 1, NOW()),
(5, 1, 'SYSTEM:DEPT', '部门管理', 'MENU', '/system/dept', 'dept', 2, 4, '部门管理页面', 1, 1, NOW()),
(6, 1, 'SYSTEM:CONFIG', '系统配置', 'MENU', '/system/config', 'config', 2, 5, '系统配置页面', 1, 1, NOW()),
(7, 1, 'SYSTEM:LOG', '日志管理', 'MENU', '/system/log', 'log', 2, 6, '日志管理页面', 1, 1, NOW()),

-- 用户管理按钮权限
(11, 2, 'SYSTEM:USER:LIST', '用户查询', 'BUTTON', NULL, NULL, 3, 1, '查询用户列表', 1, 1, NOW()),
(12, 2, 'SYSTEM:USER:CREATE', '用户新增', 'BUTTON', NULL, NULL, 3, 2, '新增用户', 1, 1, NOW()),
(13, 2, 'SYSTEM:USER:UPDATE', '用户修改', 'BUTTON', NULL, NULL, 3, 3, '修改用户', 1, 1, NOW()),
(14, 2, 'SYSTEM:USER:DELETE', '用户删除', 'BUTTON', NULL, NULL, 3, 4, '删除用户', 1, 1, NOW()),
(15, 2, 'SYSTEM:USER:RESET_PASSWORD', '重置密码', 'BUTTON', NULL, NULL, 3, 5, '重置用户密码', 1, 1, NOW()),

-- 角色管理按钮权限
(21, 3, 'SYSTEM:ROLE:LIST', '角色查询', 'BUTTON', NULL, NULL, 3, 1, '查询角色列表', 1, 1, NOW()),
(22, 3, 'SYSTEM:ROLE:CREATE', '角色新增', 'BUTTON', NULL, NULL, 3, 2, '新增角色', 1, 1, NOW()),
(23, 3, 'SYSTEM:ROLE:UPDATE', '角色修改', 'BUTTON', NULL, NULL, 3, 3, '修改角色', 1, 1, NOW()),
(24, 3, 'SYSTEM:ROLE:DELETE', '角色删除', 'BUTTON', NULL, NULL, 3, 4, '删除角色', 1, 1, NOW()),
(25, 3, 'SYSTEM:ROLE:ASSIGN_PERMISSION', '分配权限', 'BUTTON', NULL, NULL, 3, 5, '为角色分配权限', 1, 1, NOW()),

-- 权限管理按钮权限
(31, 4, 'SYSTEM:PERMISSION:LIST', '权限查询', 'BUTTON', NULL, NULL, 3, 1, '查询权限列表', 1, 1, NOW()),
(32, 4, 'SYSTEM:PERMISSION:CREATE', '权限新增', 'BUTTON', NULL, NULL, 3, 2, '新增权限', 1, 1, NOW()),
(33, 4, 'SYSTEM:PERMISSION:UPDATE', '权限修改', 'BUTTON', NULL, NULL, 3, 3, '修改权限', 1, 1, NOW()),
(34, 4, 'SYSTEM:PERMISSION:DELETE', '权限删除', 'BUTTON', NULL, NULL, 3, 4, '删除权限', 1, 1, NOW()),

-- 部门管理按钮权限
(41, 5, 'SYSTEM:DEPT:LIST', '部门查询', 'BUTTON', NULL, NULL, 3, 1, '查询部门列表', 1, 1, NOW()),
(42, 5, 'SYSTEM:DEPT:CREATE', '部门新增', 'BUTTON', NULL, NULL, 3, 2, '新增部门', 1, 1, NOW()),
(43, 5, 'SYSTEM:DEPT:UPDATE', '部门修改', 'BUTTON', NULL, NULL, 3, 3, '修改部门', 1, 1, NOW()),
(44, 5, 'SYSTEM:DEPT:DELETE', '部门删除', 'BUTTON', NULL, NULL, 3, 4, '删除部门', 1, 1, NOW()),

-- 业务模块
(100, 0, 'BUSINESS', '业务管理', 'MENU', '/business', 'business', 1, 2, '业务管理模块', 1, 1, NOW()),
(101, 100, 'BUSINESS:KINGDEE', '金蝶集成', 'MENU', '/business/kingdee', 'kingdee', 2, 1, '金蝶系统集成', 1, 1, NOW()),
(102, 100, 'BUSINESS:SYNC', '数据同步', 'MENU', '/business/sync', 'sync', 2, 2, '主数据同步管理', 1, 1, NOW()),
(103, 100, 'BUSINESS:WORKFLOW', '流程管理', 'MENU', '/business/workflow', 'workflow', 2, 3, '业务流程管理', 1, 1, NOW());

-- 初始化超级管理员用户
INSERT INTO `sys_user` (`id`, `username`, `password`, `real_name`, `nickname`, `employee_no`, `email`, `phone`, `gender`, `department_id`, `position`, `entry_date`, `status`, `create_by`, `create_time`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVWbGReyO.Tu4QbdZMiD.Ztf/7DMzFtZK4fqvQAi', '系统管理员', '管理员', 'ADMIN001', '<EMAIL>', '***********', 1, 1, '系统管理员', '2024-01-01', 1, 1, NOW());

-- 为超级管理员分配角色
INSERT INTO `sys_user_role` (`user_id`, `role_id`, `assign_by`, `assign_time`) VALUES
(1, 1, 1, NOW());

-- 为超级管理员角色分配所有权限
INSERT INTO `sys_role_permission` (`role_id`, `permission_id`, `assign_by`, `assign_time`)
SELECT 1, id, 1, NOW() FROM `sys_permission` WHERE `status` = 1;

-- 初始化系统配置
INSERT INTO `sys_config` (`config_key`, `config_value`, `config_type`, `config_group`, `config_name`, `description`, `is_system`, `status`, `create_by`, `create_time`) VALUES
('system.name', '金蝶星辰系统集成平台', 'STRING', 'SYSTEM', '系统名称', '系统显示名称', 1, 1, 1, NOW()),
('system.version', '1.0.0', 'STRING', 'SYSTEM', '系统版本', '当前系统版本号', 1, 1, 1, NOW()),
('system.copyright', '© 2024 金蝶软件(中国)有限公司', 'STRING', 'SYSTEM', '版权信息', '系统版权信息', 1, 1, 1, NOW()),
('login.max_failure_count', '5', 'NUMBER', 'SECURITY', '最大登录失败次数', '用户登录最大失败次数，超过后锁定账户', 1, 1, 1, NOW()),
('login.lock_duration', '30', 'NUMBER', 'SECURITY', '账户锁定时长', '账户锁定时长（分钟）', 1, 1, 1, NOW()),
('password.min_length', '8', 'NUMBER', 'SECURITY', '密码最小长度', '用户密码最小长度要求', 1, 1, 1, NOW()),
('password.complexity', 'true', 'BOOLEAN', 'SECURITY', '密码复杂度检查', '是否启用密码复杂度检查', 1, 1, 1, NOW()),
('session.timeout', '30', 'NUMBER', 'SECURITY', '会话超时时间', '用户会话超时时间（分钟）', 1, 1, 1, NOW()),
('file.upload.max_size', '10485760', 'NUMBER', 'FILE', '文件上传最大大小', '单个文件上传最大大小（字节）', 1, 1, 1, NOW()),
('file.upload.allowed_types', 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar', 'STRING', 'FILE', '允许上传的文件类型', '允许上传的文件扩展名，用逗号分隔', 1, 1, 1, NOW());

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 创建索引优化查询性能
CREATE INDEX idx_sys_user_dept_status ON sys_user(department_id, status);
CREATE INDEX idx_sys_user_role_user_active ON sys_user_role(user_id, is_active);
CREATE INDEX idx_sys_role_permission_role_active ON sys_role_permission(role_id, is_active);
CREATE INDEX idx_sys_operation_log_user_time ON sys_operation_log(user_id, create_time);
CREATE INDEX idx_sys_login_log_user_time ON sys_login_log(user_id, login_time);

-- 创建视图简化查询
CREATE VIEW v_user_info AS
SELECT 
    u.id,
    u.username,
    u.real_name,
    u.nickname,
    u.employee_no,
    u.email,
    u.phone,
    u.gender,
    u.avatar,
    u.position,
    u.status,
    u.is_locked,
    u.last_login_time,
    u.create_time,
    d.department_name,
    d.department_code,
    GROUP_CONCAT(r.role_name) as role_names,
    GROUP_CONCAT(r.role_code) as role_codes
FROM sys_user u
LEFT JOIN sys_department d ON u.department_id = d.id
LEFT JOIN sys_user_role ur ON u.id = ur.user_id AND ur.is_active = 1
LEFT JOIN sys_role r ON ur.role_id = r.id AND r.status = 1
WHERE u.is_deleted = 0
GROUP BY u.id;

-- 创建存储过程用于清理过期数据
DELIMITER //
CREATE PROCEDURE CleanExpiredData()
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理30天前的操作日志
    DELETE FROM sys_operation_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 30 DAY);
    
    -- 清理90天前的登录日志
    DELETE FROM sys_login_log WHERE create_time < DATE_SUB(NOW(), INTERVAL 90 DAY);
    
    -- 清理过期的临时文件
    DELETE FROM sys_file WHERE is_temp = 1 AND expire_time < NOW();
    
    COMMIT;
END //
DELIMITER ;

-- 创建定时任务清理过期数据（需要开启事件调度器）
-- SET GLOBAL event_scheduler = ON;
-- CREATE EVENT IF NOT EXISTS clean_expired_data_event
-- ON SCHEDULE EVERY 1 DAY
-- STARTS TIMESTAMP(CURRENT_DATE, '02:00:00')
-- DO CALL CleanExpiredData();

COMMIT;