package com.kingdee.integration.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * JWT工具类
 * 用于生成、解析和验证JWT令牌
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class JwtUtil {

    /**
     * JWT密钥
     */
    @Value("${jwt.secret:kingdee-integration-jwt-secret-key-2024}")
    private String secret;

    /**
     * JWT过期时间（秒）
     */
    @Value("${jwt.expiration:86400}")
    private Long expiration;

    /**
     * JWT刷新令牌过期时间（秒）
     */
    @Value("${jwt.refresh-expiration:604800}")
    private Long refreshExpiration;

    /**
     * JWT发行者
     */
    @Value("${jwt.issuer:kingdee-integration}")
    private String issuer;

    /**
     * 用户ID声明键
     */
    public static final String CLAIM_USER_ID = "userId";

    /**
     * 用户名声明键
     */
    public static final String CLAIM_USERNAME = "username";

    /**
     * 真实姓名声明键
     */
    public static final String CLAIM_REAL_NAME = "realName";

    /**
     * 部门ID声明键
     */
    public static final String CLAIM_DEPT_ID = "deptId";

    /**
     * 角色列表声明键
     */
    public static final String CLAIM_ROLES = "roles";

    /**
     * 权限列表声明键
     */
    public static final String CLAIM_PERMISSIONS = "permissions";

    /**
     * 令牌类型声明键
     */
    public static final String CLAIM_TOKEN_TYPE = "tokenType";

    /**
     * 访问令牌类型
     */
    public static final String TOKEN_TYPE_ACCESS = "access";

    /**
     * 刷新令牌类型
     */
    public static final String TOKEN_TYPE_REFRESH = "refresh";

    /**
     * 获取签名密钥
     *
     * @return 签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
        return Keys.hmacShaKeyFor(keyBytes);
    }

    /**
     * 生成访问令牌
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param realName 真实姓名
     * @param deptId 部门ID
     * @param roles 角色列表
     * @param permissions 权限列表
     * @return JWT令牌
     */
    public String generateAccessToken(Long userId, String username, String realName, 
                                    Long deptId, String roles, String permissions) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, userId);
        claims.put(CLAIM_USERNAME, username);
        claims.put(CLAIM_REAL_NAME, realName);
        claims.put(CLAIM_DEPT_ID, deptId);
        claims.put(CLAIM_ROLES, roles);
        claims.put(CLAIM_PERMISSIONS, permissions);
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_ACCESS);
        
        return generateToken(claims, username, expiration);
    }

    /**
     * 生成刷新令牌
     *
     * @param userId 用户ID
     * @param username 用户名
     * @return 刷新令牌
     */
    public String generateRefreshToken(Long userId, String username) {
        Map<String, Object> claims = new HashMap<>();
        claims.put(CLAIM_USER_ID, userId);
        claims.put(CLAIM_USERNAME, username);
        claims.put(CLAIM_TOKEN_TYPE, TOKEN_TYPE_REFRESH);
        
        return generateToken(claims, username, refreshExpiration);
    }

    /**
     * 生成令牌
     *
     * @param claims 声明
     * @param subject 主题
     * @param expiration 过期时间（秒）
     * @return JWT令牌
     */
    private String generateToken(Map<String, Object> claims, String subject, Long expiration) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + expiration * 1000);

        return Jwts.builder()
                .setClaims(claims)
                .setSubject(subject)
                .setIssuer(issuer)
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(getSigningKey(), SignatureAlgorithm.HS512)
                .compact();
    }

    /**
     * 从令牌中获取用户名
     *
     * @param token JWT令牌
     * @return 用户名
     */
    public String getUsernameFromToken(String token) {
        return getClaimFromToken(token, Claims::getSubject);
    }

    /**
     * 从令牌中获取用户ID
     *
     * @param token JWT令牌
     * @return 用户ID
     */
    public Long getUserIdFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        Object userId = claims.get(CLAIM_USER_ID);
        if (userId instanceof Integer) {
            return ((Integer) userId).longValue();
        } else if (userId instanceof Long) {
            return (Long) userId;
        }
        return null;
    }

    /**
     * 从令牌中获取真实姓名
     *
     * @param token JWT令牌
     * @return 真实姓名
     */
    public String getRealNameFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return (String) claims.get(CLAIM_REAL_NAME);
    }

    /**
     * 从令牌中获取部门ID
     *
     * @param token JWT令牌
     * @return 部门ID
     */
    public Long getDeptIdFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        Object deptId = claims.get(CLAIM_DEPT_ID);
        if (deptId instanceof Integer) {
            return ((Integer) deptId).longValue();
        } else if (deptId instanceof Long) {
            return (Long) deptId;
        }
        return null;
    }

    /**
     * 从令牌中获取角色列表
     *
     * @param token JWT令牌
     * @return 角色列表
     */
    public String getRolesFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return (String) claims.get(CLAIM_ROLES);
    }

    /**
     * 从令牌中获取权限列表
     *
     * @param token JWT令牌
     * @return 权限列表
     */
    public String getPermissionsFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return (String) claims.get(CLAIM_PERMISSIONS);
    }

    /**
     * 从令牌中获取令牌类型
     *
     * @param token JWT令牌
     * @return 令牌类型
     */
    public String getTokenTypeFromToken(String token) {
        Claims claims = getAllClaimsFromToken(token);
        return (String) claims.get(CLAIM_TOKEN_TYPE);
    }

    /**
     * 从令牌中获取过期时间
     *
     * @param token JWT令牌
     * @return 过期时间
     */
    public Date getExpirationDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getExpiration);
    }

    /**
     * 从令牌中获取签发时间
     *
     * @param token JWT令牌
     * @return 签发时间
     */
    public Date getIssuedAtDateFromToken(String token) {
        return getClaimFromToken(token, Claims::getIssuedAt);
    }

    /**
     * 从令牌中获取指定声明
     *
     * @param token JWT令牌
     * @param claimsResolver 声明解析器
     * @param <T> 声明类型
     * @return 声明值
     */
    public <T> T getClaimFromToken(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = getAllClaimsFromToken(token);
        return claimsResolver.apply(claims);
    }

    /**
     * 从令牌中获取所有声明
     *
     * @param token JWT令牌
     * @return 所有声明
     */
    private Claims getAllClaimsFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getBody();
        } catch (Exception e) {
            log.error("解析JWT令牌失败: {}", e.getMessage());
            throw new JwtException("无效的JWT令牌", e);
        }
    }

    /**
     * 检查令牌是否过期
     *
     * @param token JWT令牌
     * @return 是否过期
     */
    public Boolean isTokenExpired(String token) {
        try {
            final Date expiration = getExpirationDateFromToken(token);
            return expiration.before(new Date());
        } catch (Exception e) {
            log.error("检查令牌过期状态失败: {}", e.getMessage());
            return true;
        }
    }

    /**
     * 验证令牌
     *
     * @param token JWT令牌
     * @param username 用户名
     * @return 是否有效
     */
    public Boolean validateToken(String token, String username) {
        try {
            final String tokenUsername = getUsernameFromToken(token);
            return (username.equals(tokenUsername) && !isTokenExpired(token));
        } catch (Exception e) {
            log.error("验证JWT令牌失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证访问令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public Boolean validateAccessToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_ACCESS.equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            log.error("验证访问令牌失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 验证刷新令牌
     *
     * @param token JWT令牌
     * @return 是否有效
     */
    public Boolean validateRefreshToken(String token) {
        try {
            String tokenType = getTokenTypeFromToken(token);
            return TOKEN_TYPE_REFRESH.equals(tokenType) && !isTokenExpired(token);
        } catch (Exception e) {
            log.error("验证刷新令牌失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @param roles 角色列表
     * @param permissions 权限列表
     * @return 新的访问令牌
     */
    public String refreshAccessToken(String refreshToken, String roles, String permissions) {
        if (!validateRefreshToken(refreshToken)) {
            throw new JwtException("无效的刷新令牌");
        }

        Long userId = getUserIdFromToken(refreshToken);
        String username = getUsernameFromToken(refreshToken);
        String realName = getRealNameFromToken(refreshToken);
        Long deptId = getDeptIdFromToken(refreshToken);

        return generateAccessToken(userId, username, realName, deptId, roles, permissions);
    }

    /**
     * 获取令牌剩余有效时间（秒）
     *
     * @param token JWT令牌
     * @return 剩余有效时间
     */
    public Long getTokenRemainingTime(String token) {
        try {
            Date expiration = getExpirationDateFromToken(token);
            Date now = new Date();
            if (expiration.before(now)) {
                return 0L;
            }
            return (expiration.getTime() - now.getTime()) / 1000;
        } catch (Exception e) {
            log.error("获取令牌剩余时间失败: {}", e.getMessage());
            return 0L;
        }
    }

    /**
     * 检查令牌是否即将过期（30分钟内）
     *
     * @param token JWT令牌
     * @return 是否即将过期
     */
    public Boolean isTokenExpiringSoon(String token) {
        try {
            Long remainingTime = getTokenRemainingTime(token);
            return remainingTime > 0 && remainingTime <= 1800; // 30分钟
        } catch (Exception e) {
            log.error("检查令牌即将过期状态失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 解析令牌头部信息
     *
     * @param token JWT令牌
     * @return 头部信息
     */
    public JwsHeader getHeaderFromToken(String token) {
        try {
            return Jwts.parserBuilder()
                    .setSigningKey(getSigningKey())
                    .build()
                    .parseClaimsJws(token)
                    .getHeader();
        } catch (Exception e) {
            log.error("解析JWT令牌头部失败: {}", e.getMessage());
            throw new JwtException("无效的JWT令牌", e);
        }
    }

    /**
     * 获取令牌签名算法
     *
     * @param token JWT令牌
     * @return 签名算法
     */
    public String getAlgorithmFromToken(String token) {
        JwsHeader header = getHeaderFromToken(token);
        return header.getAlgorithm();
    }

    /**
     * 创建令牌黑名单键
     *
     * @param token JWT令牌
     * @return 黑名单键
     */
    public String createBlacklistKey(String token) {
        try {
            String username = getUsernameFromToken(token);
            Date issuedAt = getIssuedAtDateFromToken(token);
            return String.format("jwt:blacklist:%s:%d", username, issuedAt.getTime());
        } catch (Exception e) {
            log.error("创建令牌黑名单键失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 获取配置信息
     *
     * @return 配置信息映射
     */
    public Map<String, Object> getJwtConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("expiration", expiration);
        config.put("refreshExpiration", refreshExpiration);
        config.put("issuer", issuer);
        config.put("algorithm", "HS512");
        return config;
    }

    /**
     * 验证令牌格式
     *
     * @param token JWT令牌
     * @return 是否格式正确
     */
    public Boolean isValidTokenFormat(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        
        // JWT令牌应该包含两个点号，分为三部分
        String[] parts = token.split("\\.");
        return parts.length == 3;
    }

    /**
     * 从Authorization头中提取令牌
     *
     * @param authorizationHeader Authorization头
     * @return JWT令牌
     */
    public String extractTokenFromHeader(String authorizationHeader) {
        if (authorizationHeader != null && authorizationHeader.startsWith("Bearer ")) {
            return authorizationHeader.substring(7);
        }
        return null;
    }
}