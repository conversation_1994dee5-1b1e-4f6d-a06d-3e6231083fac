package com.kingdee.integration.security;

import com.alibaba.fastjson2.JSON;
import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.common.result.ResultCode;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * JWT认证入口点
 * 处理未认证的请求
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, 
                        HttpServletResponse response,
                        AuthenticationException authException) throws IOException, ServletException {
        
        log.warn("未认证的请求访问: {} {}, 异常: {}", 
                request.getMethod(), 
                request.getRequestURI(), 
                authException.getMessage());
        
        // 设置响应头
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        
        // 允许跨域
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        
        // 构建错误响应
        Result<Object> result = Result.error(ResultCode.UNAUTHORIZED, "访问被拒绝，请先登录");
        
        // 添加详细错误信息
        if (authException.getMessage() != null) {
            if (authException.getMessage().contains("JWT")) {
                result = Result.error(ResultCode.TOKEN_INVALID, "令牌无效或已过期，请重新登录");
            } else if (authException.getMessage().contains("expired")) {
                result = Result.error(ResultCode.TOKEN_EXPIRED, "令牌已过期，请重新登录");
            } else if (authException.getMessage().contains("malformed")) {
                result = Result.error(ResultCode.TOKEN_INVALID, "令牌格式错误");
            }
        }
        
        // 写入响应
        String jsonResponse = JSON.toJSONString(result);
        response.getWriter().write(jsonResponse);
        response.getWriter().flush();
    }
}