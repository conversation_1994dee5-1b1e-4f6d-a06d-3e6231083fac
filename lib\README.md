# SDK 库文件目录

本目录包含项目所需的第三方 SDK JAR 文件，主要用于与金蝶云星辰系统的集成。

## 目录结构

```
lib/
├── README.md                                          # 本文件
├── kingdee-xw-openapi-1.0.0-jar-with-dependencies.jar # 金蝶云星辰 Java SDK
├── apigw-client-1.0.0.jar                            # API 网关客户端 SDK
└── xingchen-auth-sdk-1.0.0.jar                       # 星辰认证 SDK
```

## SDK 文件说明

### 1. 金蝶云星辰 Java SDK
- **文件名**: `kingdee-xw-openapi-1.0.0-jar-with-dependencies.jar`
- **版本**: 1.0.0
- **用途**: 提供与金蝶云星辰系统的完整 API 集成功能
- **主要功能**:
  - 主数据管理（物料、客户、供应商等）
  - 业务单据操作（采购订单、销售订单等）
  - 组织架构同步
  - 权限管理

### 2. API 网关客户端 SDK
- **文件名**: `apigw-client-1.0.0.jar`
- **版本**: 1.0.0
- **用途**: 提供 API 网关的客户端功能
- **主要功能**:
  - HTTP 请求封装
  - 认证令牌管理
  - 请求签名
  - 错误处理

### 3. 星辰认证 SDK
- **文件名**: `xingchen-auth-sdk-1.0.0.jar`
- **版本**: 1.0.0
- **用途**: 提供星辰系统的认证功能
- **主要功能**:
  - 客户端认证
  - 访问令牌获取
  - 令牌刷新
  - 签名验证

## Maven 依赖配置

在项目的 `pom.xml` 文件中添加以下依赖配置：

```xml
<!-- 金蝶云星辰 Java SDK -->
<dependency>
    <groupId>kingdee-xw-openapi</groupId>
    <artifactId>kingdee-xw-openapi</artifactId>
    <version>1.0.0</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/lib/kingdee-xw-openapi-1.0.0-jar-with-dependencies.jar</systemPath>
</dependency>

<!-- API 网关客户端 SDK -->
<dependency>
    <groupId>com.kingdee.apigw</groupId>
    <artifactId>apigw-client</artifactId>
    <version>1.0.0</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/lib/apigw-client-1.0.0.jar</systemPath>
</dependency>

<!-- 星辰认证 SDK -->
<dependency>
    <groupId>com.kingdee.xingchen</groupId>
    <artifactId>xingchen-auth-sdk</artifactId>
    <version>1.0.0</version>
    <scope>system</scope>
    <systemPath>${project.basedir}/lib/xingchen-auth-sdk-1.0.0.jar</systemPath>
</dependency>
```

## 使用说明

### 1. 基本配置

在 Spring Boot 项目中，首先需要在 `application.yml` 中配置相关参数：

```yaml
# 金蝶云星辰配置
kingdee:
  client_id: your-client-id
  client_secret: your-client-secret

# 星辰认证配置
xingchen:
  client-id: your-client-id
  client-secret: your-client-secret
  app-key: your-app-key
  app-signature: your-app-signature
```

### 2. 认证示例

参考 `requirements.md` 文件中的 "XingChen (星辰) 认证示例" 章节，了解如何使用认证 SDK。

### 3. API 调用示例

```java
// 初始化客户端
ApigwClient client = ApigwClient.getInstance();
client.init(config);

// 创建请求
ApiRequest request = new ApiRequest(HttpMethod.GET, "api.kingdee.com", "/api/path");
request.setQuerys(params);

// 发送请求
ApiResult result = client.send(request);
```

## 注意事项

1. **版本兼容性**: 请确保 SDK 版本与金蝶云星辰系统版本兼容
2. **安全性**: 
   - 不要将 `client_secret` 等敏感信息提交到版本控制系统
   - 建议使用环境变量或加密配置文件存储敏感信息
3. **更新维护**: 
   - 定期检查 SDK 更新
   - 关注金蝶官方发布的安全补丁
4. **性能优化**:
   - 合理配置连接池参数
   - 实现令牌缓存机制
   - 添加重试和熔断机制

## 获取 SDK

如果需要获取最新版本的 SDK 文件，请：

1. 访问金蝶云星辰开发者平台
2. 登录开发者账号
3. 在 SDK 下载页面获取最新版本
4. 将下载的 JAR 文件放置到本目录下
5. 更新 `pom.xml` 中的版本号和文件路径

## 技术支持

如果在使用 SDK 过程中遇到问题，可以：

1. 查阅金蝶云星辰官方文档
2. 访问开发者社区论坛
3. 联系金蝶技术支持团队
4. 查看项目中的 `requirements.md` 开发指南

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本
- 包含基础的认证和 API 调用功能
- 支持主数据同步
- 支持业务单据操作
