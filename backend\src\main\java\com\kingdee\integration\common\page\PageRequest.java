package com.kingdee.integration.common.page;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * 分页查询请求类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@Schema(description = "分页查询请求")
public class PageRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 页码，从1开始
     */
    @Schema(description = "页码，从1开始", example = "1")
    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    @NotNull(message = "每页大小不能为空")
    @Min(value = 1, message = "每页大小必须大于0")
    @Max(value = 1000, message = "每页大小不能超过1000")
    private Integer pageSize = 10;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段", example = "createTime")
    private String orderBy;

    /**
     * 排序方向：asc-升序，desc-降序
     */
    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String orderDirection = "desc";

    /**
     * 多字段排序
     */
    @Schema(description = "多字段排序")
    private List<SortField> sorts;

    /**
     * 是否查询总数
     */
    @Schema(description = "是否查询总数", example = "true")
    private Boolean searchCount = true;

    /**
     * 获取偏移量
     * 
     * @return 偏移量
     */
    public Integer getOffset() {
        return (pageNum - 1) * pageSize;
    }

    /**
     * 获取限制数量
     * 
     * @return 限制数量
     */
    public Integer getLimit() {
        return pageSize;
    }

    /**
     * 获取排序SQL
     * 
     * @return 排序SQL
     */
    public String getOrderBySql() {
        if (sorts != null && !sorts.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < sorts.size(); i++) {
                SortField sort = sorts.get(i);
                if (i > 0) {
                    sb.append(", ");
                }
                sb.append(sort.getField()).append(" ").append(sort.getDirection());
            }
            return sb.toString();
        } else if (orderBy != null && !orderBy.trim().isEmpty()) {
            return orderBy + " " + (orderDirection != null ? orderDirection : "desc");
        }
        return null;
    }

    /**
     * 添加排序字段
     * 
     * @param field 字段名
     * @param direction 排序方向
     * @return 当前对象
     */
    public PageRequest addSort(String field, String direction) {
        if (sorts == null) {
            sorts = new java.util.ArrayList<>();
        }
        sorts.add(new SortField(field, direction));
        return this;
    }

    /**
     * 添加升序排序字段
     * 
     * @param field 字段名
     * @return 当前对象
     */
    public PageRequest addSortAsc(String field) {
        return addSort(field, "asc");
    }

    /**
     * 添加降序排序字段
     * 
     * @param field 字段名
     * @return 当前对象
     */
    public PageRequest addSortDesc(String field) {
        return addSort(field, "desc");
    }

    /**
     * 清空排序
     * 
     * @return 当前对象
     */
    public PageRequest clearSort() {
        this.sorts = null;
        this.orderBy = null;
        this.orderDirection = "desc";
        return this;
    }

    /**
     * 设置不查询总数
     * 
     * @return 当前对象
     */
    public PageRequest noSearchCount() {
        this.searchCount = false;
        return this;
    }

    /**
     * 验证分页参数
     * 
     * @return 是否有效
     */
    public boolean isValid() {
        return pageNum != null && pageNum > 0 && pageSize != null && pageSize > 0 && pageSize <= 1000;
    }

    /**
     * 创建默认分页请求
     * 
     * @return 分页请求
     */
    public static PageRequest of() {
        return new PageRequest();
    }

    /**
     * 创建分页请求
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 分页请求
     */
    public static PageRequest of(Integer pageNum, Integer pageSize) {
        return new PageRequest().setPageNum(pageNum).setPageSize(pageSize);
    }

    /**
     * 创建分页请求
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param orderBy 排序字段
     * @param orderDirection 排序方向
     * @return 分页请求
     */
    public static PageRequest of(Integer pageNum, Integer pageSize, String orderBy, String orderDirection) {
        return new PageRequest()
                .setPageNum(pageNum)
                .setPageSize(pageSize)
                .setOrderBy(orderBy)
                .setOrderDirection(orderDirection);
    }

    /**
     * 排序字段内部类
     */
    @Data
    @Accessors(chain = true)
    @Schema(description = "排序字段")
    public static class SortField implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 字段名
         */
        @Schema(description = "字段名", example = "createTime")
        @NotNull(message = "排序字段不能为空")
        private String field;

        /**
         * 排序方向：asc-升序，desc-降序
         */
        @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
        private String direction = "desc";

        public SortField() {
        }

        public SortField(String field, String direction) {
            this.field = field;
            this.direction = direction;
        }

        /**
         * 创建升序排序字段
         * 
         * @param field 字段名
         * @return 排序字段
         */
        public static SortField asc(String field) {
            return new SortField(field, "asc");
        }

        /**
         * 创建降序排序字段
         * 
         * @param field 字段名
         * @return 排序字段
         */
        public static SortField desc(String field) {
            return new SortField(field, "desc");
        }

        /**
         * 验证排序字段
         * 
         * @return 是否有效
         */
        public boolean isValid() {
            return field != null && !field.trim().isEmpty() && 
                   ("asc".equalsIgnoreCase(direction) || "desc".equalsIgnoreCase(direction));
        }
    }
}