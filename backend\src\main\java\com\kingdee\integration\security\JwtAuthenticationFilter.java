package com.kingdee.integration.security;

import com.kingdee.integration.service.UserService;
import com.kingdee.integration.util.JwtUtil;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;
import java.util.Arrays;

/**
 * JWT认证过滤器
 * 处理JWT令牌的验证和用户认证
 * 
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserService userService;

    /**
     * 不需要JWT验证的路径
     */
    private static final String[] EXCLUDED_PATHS = {
        "/api/auth/",
        "/api/public/",
        "/swagger-ui/",
        "/v3/api-docs/",
        "/swagger-resources/",
        "/webjars/",
        "/favicon.ico",
        "/actuator/health",
        "/actuator/info",
        "/css/",
        "/js/",
        "/images/",
        "/fonts/",
        "/static/"
    };

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                   @NonNull HttpServletResponse response,
                                   @NonNull FilterChain filterChain) throws ServletException, IOException {
        
        try {
            // 检查是否需要跳过JWT验证
            if (shouldSkipFilter(request)) {
                filterChain.doFilter(request, response);
                return;
            }

            // 从请求头中获取JWT令牌
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt)) {
                try {
                    // 验证JWT令牌
                    if (jwtUtil.validateToken(jwt)) {
                        // 从JWT中获取用户名
                        String username = jwtUtil.getUsernameFromToken(jwt);
                        
                        if (StringUtils.hasText(username) && SecurityContextHolder.getContext().getAuthentication() == null) {
                            // 加载用户详情
                            UserDetails userDetails = userService.findByUsername(username);
                            
                            if (userDetails != null) {
                                // 创建认证对象
                                UsernamePasswordAuthenticationToken authentication = 
                                    new UsernamePasswordAuthenticationToken(
                                        userDetails, 
                                        null, 
                                        userDetails.getAuthorities()
                                    );
                                
                                // 设置认证详情
                                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                                
                                // 设置到安全上下文
                                SecurityContextHolder.getContext().setAuthentication(authentication);
                                
                                log.debug("用户 {} 认证成功", username);
                            } else {
                                log.warn("用户 {} 不存在", username);
                            }
                        }
                    } else {
                        log.warn("JWT令牌验证失败: {}", jwt.substring(0, Math.min(jwt.length(), 20)) + "...");
                    }
                } catch (Exception e) {
                    log.error("JWT令牌处理异常: {}", e.getMessage());
                }
            } else {
                log.debug("请求中未找到JWT令牌: {} {}", request.getMethod(), request.getRequestURI());
            }
            
        } catch (Exception e) {
            log.error("JWT认证过滤器异常: {}", e.getMessage(), e);
        }
        
        // 继续过滤器链
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取JWT令牌
     *
     * @param request HTTP请求
     * @return JWT令牌
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        // 从Authorization头获取
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        
        // 从请求参数获取（用于某些特殊场景，如文件下载）
        String tokenParam = request.getParameter("token");
        if (StringUtils.hasText(tokenParam)) {
            return tokenParam;
        }
        
        // 从Cookie获取（可选）
        if (request.getCookies() != null) {
            for (var cookie : request.getCookies()) {
                if ("jwt-token".equals(cookie.getName())) {
                    return cookie.getValue();
                }
            }
        }
        
        return null;
    }

    /**
     * 判断是否应该跳过过滤器
     *
     * @param request HTTP请求
     * @return 是否跳过
     */
    private boolean shouldSkipFilter(HttpServletRequest request) {
        String requestURI = request.getRequestURI();
        
        // 检查是否在排除路径中
        return Arrays.stream(EXCLUDED_PATHS)
                .anyMatch(requestURI::startsWith);
    }

    /**
     * 判断是否为OPTIONS请求
     *
     * @param request HTTP请求
     * @return 是否为OPTIONS请求
     */
    private boolean isOptionsRequest(HttpServletRequest request) {
        return "OPTIONS".equalsIgnoreCase(request.getMethod());
    }

    /**
     * 设置CORS响应头
     *
     * @param response HTTP响应
     */
    private void setCorsHeaders(HttpServletResponse response) {
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", 
            "Authorization, Content-Type, X-Requested-With, Accept, Origin, Access-Control-Request-Method, Access-Control-Request-Headers");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Max-Age", "3600");
    }

    @Override
    protected boolean shouldNotFilter(HttpServletRequest request) throws ServletException {
        // 对于OPTIONS请求，直接跳过
        if (isOptionsRequest(request)) {
            return true;
        }
        
        // 对于排除路径，跳过过滤
        return shouldSkipFilter(request);
    }
}