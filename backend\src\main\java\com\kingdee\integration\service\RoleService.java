package com.kingdee.integration.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.kingdee.integration.entity.Role;

import java.util.List;

/**
 * 角色服务接口
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
public interface RoleService extends IService<Role> {

    /**
     * 根据角色编码查询角色
     *
     * @param roleCode 角色编码
     * @return 角色信息
     */
    Role findByRoleCode(String roleCode);

    /**
     * 根据角色名称查询角色
     *
     * @param roleName 角色名称
     * @return 角色信息
     */
    Role findByRoleName(String roleName);

    /**
     * 分页查询角色列表
     *
     * @param page 分页参数
     * @param roleCode 角色编码（模糊查询）
     * @param roleName 角色名称（模糊查询）
     * @param status 状态
     * @param roleType 角色类型
     * @return 角色分页列表
     */
    IPage<Role> pageRoles(Page<Role> page, String roleCode, String roleName, 
                         Integer status, String roleType);

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 创建的角色
     */
    Role createRole(Role role);

    /**
     * 更新角色
     *
     * @param role 角色信息
     * @return 更新的角色
     */
    Role updateRole(Role role);

    /**
     * 删除角色
     *
     * @param roleId 角色ID
     * @return 是否删除成功
     */
    boolean deleteRole(Long roleId);

    /**
     * 批量删除角色
     *
     * @param roleIds 角色ID列表
     * @return 是否删除成功
     */
    boolean deleteRoles(List<Long> roleIds);

    /**
     * 启用角色
     *
     * @param roleId 角色ID
     * @return 是否启用成功
     */
    boolean enableRole(Long roleId);

    /**
     * 禁用角色
     *
     * @param roleId 角色ID
     * @return 是否禁用成功
     */
    boolean disableRole(Long roleId);

    /**
     * 根据用户ID查询角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getRolesByUserId(Long userId);

    /**
     * 获取所有启用的角色
     *
     * @return 角色列表
     */
    List<Role> getEnabledRoles();

    /**
     * 获取系统角色列表
     *
     * @return 系统角色列表
     */
    List<Role> getSystemRoles();

    /**
     * 获取业务角色列表
     *
     * @return 业务角色列表
     */
    List<Role> getBusinessRoles();

    /**
     * 分配角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否分配成功
     */
    boolean assignPermissions(Long roleId, List<Long> permissionIds);

    /**
     * 移除角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     * @return 是否移除成功
     */
    boolean removePermissions(Long roleId, List<Long> permissionIds);

    /**
     * 获取角色权限列表
     *
     * @param roleId 角色ID
     * @return 权限编码列表
     */
    List<String> getRolePermissions(Long roleId);

    /**
     * 获取角色权限ID列表
     *
     * @param roleId 角色ID
     * @return 权限ID列表
     */
    List<Long> getRolePermissionIds(Long roleId);

    /**
     * 检查角色是否有指定权限
     *
     * @param roleId 角色ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(Long roleId, String permissionCode);

    /**
     * 检查角色编码是否存在
     *
     * @param roleCode 角色编码
     * @param excludeRoleId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByRoleCode(String roleCode, Long excludeRoleId);

    /**
     * 检查角色名称是否存在
     *
     * @param roleName 角色名称
     * @param excludeRoleId 排除的角色ID（用于更新时检查）
     * @return 是否存在
     */
    boolean existsByRoleName(String roleName, Long excludeRoleId);

    /**
     * 统计角色数量
     *
     * @param status 状态（可选）
     * @param roleType 角色类型（可选）
     * @return 角色数量
     */
    Long countRoles(Integer status, String roleType);

    /**
     * 获取角色用户数量
     *
     * @param roleId 角色ID
     * @return 用户数量
     */
    Long getRoleUserCount(Long roleId);

    /**
     * 复制角色
     *
     * @param sourceRoleId 源角色ID
     * @param newRoleCode 新角色编码
     * @param newRoleName 新角色名称
     * @param copyPermissions 是否复制权限
     * @return 新角色
     */
    Role copyRole(Long sourceRoleId, String newRoleCode, String newRoleName, boolean copyPermissions);

    /**
     * 导入角色
     *
     * @param roles 角色列表
     * @param updateExisting 是否更新已存在的角色
     * @return 导入结果
     */
    ImportResult importRoles(List<Role> roles, boolean updateExisting);

    /**
     * 导出角色
     *
     * @param roleIds 角色ID列表（为空则导出所有）
     * @return 角色列表
     */
    List<Role> exportRoles(List<Long> roleIds);

    /**
     * 同步角色权限缓存
     *
     * @param roleId 角色ID（为空则同步所有）
     */
    void syncRolePermissionCache(Long roleId);

    /**
     * 清除角色权限缓存
     *
     * @param roleId 角色ID（为空则清除所有）
     */
    void clearRolePermissionCache(Long roleId);

    /**
     * 导入结果类
     */
    class ImportResult {
        private int totalCount;
        private int successCount;
        private int failureCount;
        private List<String> errorMessages;

        // 构造函数、getter、setter
        public ImportResult(int totalCount, int successCount, int failureCount, List<String> errorMessages) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.failureCount = failureCount;
            this.errorMessages = errorMessages;
        }

        // Getters
        public int getTotalCount() { return totalCount; }
        public int getSuccessCount() { return successCount; }
        public int getFailureCount() { return failureCount; }
        public List<String> getErrorMessages() { return errorMessages; }
    }
}