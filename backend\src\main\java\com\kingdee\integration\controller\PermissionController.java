package com.kingdee.integration.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.kingdee.integration.common.result.Result;
import com.kingdee.integration.entity.Permission;
import com.kingdee.integration.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 权限管理控制器
 *
 * <AUTHOR>
 * @since 2024-01-15
 */
@Slf4j
@RestController
@RequestMapping("/api/permissions")
@RequiredArgsConstructor
@Validated
@Tag(name = "权限管理", description = "权限管理相关接口")
public class PermissionController {

    private final PermissionService permissionService;

    /**
     * 分页查询权限列表
     */
    @GetMapping
    @Operation(summary = "分页查询权限列表", description = "根据条件分页查询权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<IPage<Permission>> pagePermissions(
            @Parameter(description = "页码", example = "1") @RequestParam(defaultValue = "1") Long current,
            @Parameter(description = "每页大小", example = "10") @RequestParam(defaultValue = "10") Long size,
            @Parameter(description = "权限编码") @RequestParam(required = false) String permissionCode,
            @Parameter(description = "权限名称") @RequestParam(required = false) String permissionName,
            @Parameter(description = "权限类型") @RequestParam(required = false) String permissionType,
            @Parameter(description = "父权限ID") @RequestParam(required = false) Long parentId,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status) {
        
        Page<Permission> page = new Page<>(current, size);
        IPage<Permission> result = permissionService.pagePermissions(page, permissionCode, permissionName, permissionType, parentId, status);
        return Result.success(result);
    }

    /**
     * 获取权限树结构
     */
    @GetMapping("/tree")
    @Operation(summary = "获取权限树", description = "获取权限的树形结构")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getPermissionTree(
            @Parameter(description = "权限类型") @RequestParam(required = false) String permissionType,
            @Parameter(description = "是否只显示启用的") @RequestParam(defaultValue = "false") Boolean enabledOnly) {
        
        List<Permission> tree = permissionService.getPermissionTree(permissionType, enabledOnly);
        return Result.success(tree);
    }

    /**
     * 根据ID查询权限详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "查询权限详情", description = "根据权限ID查询权限详情")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<Permission> getPermissionById(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        Permission permission = permissionService.getById(id);
        return Result.success(permission);
    }

    /**
     * 根据权限编码查询权限
     */
    @GetMapping("/by-code/{permissionCode}")
    @Operation(summary = "根据编码查询权限", description = "根据权限编码查询权限")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<Permission> getPermissionByCode(
            @Parameter(description = "权限编码", required = true) @PathVariable @NotEmpty String permissionCode) {
        
        Permission permission = permissionService.getPermissionByCode(permissionCode);
        return Result.success(permission);
    }

    /**
     * 创建权限
     */
    @PostMapping
    @Operation(summary = "创建权限", description = "创建新权限")
    @PreAuthorize("hasPermission('permission:create')")
    public Result<Permission> createPermission(
            @Parameter(description = "权限信息", required = true) @RequestBody @Valid Permission permission) {
        
        Permission createdPermission = permissionService.createPermission(permission);
        return Result.success(createdPermission);
    }

    /**
     * 更新权限
     */
    @PutMapping("/{id}")
    @Operation(summary = "更新权限", description = "更新权限信息")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Permission> updatePermission(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "权限信息", required = true) @RequestBody @Valid Permission permission) {
        
        permission.setId(id);
        Permission updatedPermission = permissionService.updatePermission(permission);
        return Result.success(updatedPermission);
    }

    /**
     * 删除权限
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "删除权限", description = "根据权限ID删除权限")
    @PreAuthorize("hasPermission('permission:delete')")
    public Result<Void> deletePermission(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean deleted = permissionService.deletePermission(id);
        return deleted ? Result.success() : Result.error("删除失败");
    }

    /**
     * 批量删除权限
     */
    @DeleteMapping
    @Operation(summary = "批量删除权限", description = "根据权限ID列表批量删除权限")
    @PreAuthorize("hasPermission('permission:delete')")
    public Result<Void> deletePermissions(
            @Parameter(description = "权限ID列表", required = true) @RequestBody @NotEmpty List<Long> permissionIds) {
        
        boolean deleted = permissionService.deletePermissions(permissionIds);
        return deleted ? Result.success() : Result.error("批量删除失败");
    }

    /**
     * 启用权限
     */
    @PutMapping("/{id}/enable")
    @Operation(summary = "启用权限", description = "启用指定权限")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Void> enablePermission(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean enabled = permissionService.enablePermission(id);
        return enabled ? Result.success() : Result.error("启用失败");
    }

    /**
     * 禁用权限
     */
    @PutMapping("/{id}/disable")
    @Operation(summary = "禁用权限", description = "禁用指定权限")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Void> disablePermission(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean disabled = permissionService.disablePermission(id);
        return disabled ? Result.success() : Result.error("禁用失败");
    }

    /**
     * 批量更新权限状态
     */
    @PutMapping("/batch-status")
    @Operation(summary = "批量更新状态", description = "批量更新权限状态")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Void> batchUpdateStatus(
            @Parameter(description = "权限ID列表", required = true) @RequestBody @NotEmpty List<Long> permissionIds,
            @Parameter(description = "状态", required = true) @RequestParam @NotNull Integer status) {
        
        boolean updated = permissionService.batchUpdateStatus(permissionIds, status);
        return updated ? Result.success() : Result.error("批量更新失败");
    }

    /**
     * 获取所有启用的权限
     */
    @GetMapping("/enabled")
    @Operation(summary = "获取启用权限", description = "获取所有启用状态的权限")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getEnabledPermissions(
            @Parameter(description = "权限类型") @RequestParam(required = false) String permissionType) {
        
        List<Permission> permissions = permissionService.getEnabledPermissions(permissionType);
        return Result.success(permissions);
    }

    /**
     * 根据权限类型查询权限列表
     */
    @GetMapping("/by-type/{permissionType}")
    @Operation(summary = "根据类型查询权限", description = "根据权限类型查询权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getPermissionsByType(
            @Parameter(description = "权限类型", required = true) @PathVariable @NotEmpty String permissionType) {
        
        List<Permission> permissions = permissionService.getPermissionsByType(permissionType);
        return Result.success(permissions);
    }

    /**
     * 根据角色ID查询权限列表
     */
    @GetMapping("/by-role/{roleId}")
    @Operation(summary = "根据角色查询权限", description = "根据角色ID查询权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getPermissionsByRoleId(
            @Parameter(description = "角色ID", required = true) @PathVariable @NotNull Long roleId) {
        
        List<Permission> permissions = permissionService.getPermissionsByRoleId(roleId);
        return Result.success(permissions);
    }

    /**
     * 根据用户ID查询权限列表
     */
    @GetMapping("/by-user/{userId}")
    @Operation(summary = "根据用户查询权限", description = "根据用户ID查询权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getPermissionsByUserId(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long userId) {
        
        List<Permission> permissions = permissionService.getPermissionsByUserId(userId);
        return Result.success(permissions);
    }

    /**
     * 获取菜单权限列表
     */
    @GetMapping("/menus")
    @Operation(summary = "获取菜单权限", description = "获取菜单类型的权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getMenuPermissions(
            @Parameter(description = "是否只显示启用的") @RequestParam(defaultValue = "true") Boolean enabledOnly) {
        
        List<Permission> menus = permissionService.getMenuPermissions(enabledOnly);
        return Result.success(menus);
    }

    /**
     * 获取按钮权限列表
     */
    @GetMapping("/buttons")
    @Operation(summary = "获取按钮权限", description = "获取按钮类型的权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getButtonPermissions(
            @Parameter(description = "父权限ID") @RequestParam(required = false) Long parentId,
            @Parameter(description = "是否只显示启用的") @RequestParam(defaultValue = "true") Boolean enabledOnly) {
        
        List<Permission> buttons = permissionService.getButtonPermissions(parentId, enabledOnly);
        return Result.success(buttons);
    }

    /**
     * 获取API权限列表
     */
    @GetMapping("/apis")
    @Operation(summary = "获取API权限", description = "获取API类型的权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getApiPermissions(
            @Parameter(description = "是否只显示启用的") @RequestParam(defaultValue = "true") Boolean enabledOnly) {
        
        List<Permission> apis = permissionService.getApiPermissions(enabledOnly);
        return Result.success(apis);
    }

    /**
     * 获取子权限列表
     */
    @GetMapping("/{id}/children")
    @Operation(summary = "获取子权限", description = "获取指定权限的子权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getChildPermissions(
            @Parameter(description = "父权限ID", required = true) @PathVariable @NotNull Long id,
            @Parameter(description = "是否只显示启用的") @RequestParam(defaultValue = "false") Boolean enabledOnly) {
        
        List<Permission> children = permissionService.getChildPermissions(id, enabledOnly);
        return Result.success(children);
    }

    /**
     * 获取权限路径
     */
    @GetMapping("/{id}/path")
    @Operation(summary = "获取权限路径", description = "获取权限的完整路径")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<String> getPermissionPath(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        String path = permissionService.getPermissionPath(id);
        return Result.success(path);
    }

    /**
     * 检查权限编码是否存在
     */
    @GetMapping("/check-code")
    @Operation(summary = "检查权限编码", description = "检查权限编码是否已存在")
    public Result<Boolean> checkPermissionCode(
            @Parameter(description = "权限编码", required = true) @RequestParam @NotEmpty String permissionCode,
            @Parameter(description = "排除的权限ID") @RequestParam(required = false) Long excludePermissionId) {
        
        boolean exists = permissionService.existsByPermissionCode(permissionCode, excludePermissionId);
        return Result.success(exists);
    }

    /**
     * 检查权限名称是否存在
     */
    @GetMapping("/check-name")
    @Operation(summary = "检查权限名称", description = "检查权限名称是否已存在")
    public Result<Boolean> checkPermissionName(
            @Parameter(description = "权限名称", required = true) @RequestParam @NotEmpty String permissionName,
            @Parameter(description = "父权限ID") @RequestParam(required = false) Long parentId,
            @Parameter(description = "排除的权限ID") @RequestParam(required = false) Long excludePermissionId) {
        
        boolean exists = permissionService.existsByPermissionName(permissionName, parentId, excludePermissionId);
        return Result.success(exists);
    }

    /**
     * 检查权限URL是否存在
     */
    @GetMapping("/check-url")
    @Operation(summary = "检查权限URL", description = "检查权限URL是否已存在")
    public Result<Boolean> checkPermissionUrl(
            @Parameter(description = "权限URL", required = true) @RequestParam @NotEmpty String permissionUrl,
            @Parameter(description = "HTTP方法") @RequestParam(required = false) String httpMethod,
            @Parameter(description = "排除的权限ID") @RequestParam(required = false) Long excludePermissionId) {
        
        boolean exists = permissionService.existsByPermissionUrl(permissionUrl, httpMethod, excludePermissionId);
        return Result.success(exists);
    }

    /**
     * 统计权限数量
     */
    @GetMapping("/count")
    @Operation(summary = "统计权限数量", description = "根据条件统计权限数量")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<Long> countPermissions(
            @Parameter(description = "权限类型") @RequestParam(required = false) String permissionType,
            @Parameter(description = "状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "父权限ID") @RequestParam(required = false) Long parentId) {
        
        Long count = permissionService.countPermissions(permissionType, status, parentId);
        return Result.success(count);
    }

    /**
     * 获取最大排序号
     */
    @GetMapping("/max-sort")
    @Operation(summary = "获取最大排序号", description = "获取指定父权限下的最大排序号")
    @PreAuthorize("hasPermission('permission:create')")
    public Result<Integer> getMaxSortOrder(
            @Parameter(description = "父权限ID") @RequestParam(required = false) Long parentId) {
        
        Integer maxSort = permissionService.getMaxSortOrder(parentId);
        return Result.success(maxSort);
    }

    /**
     * 获取权限详情（包含父权限信息）
     */
    @GetMapping("/{id}/detail")
    @Operation(summary = "获取权限详情", description = "获取权限详情及其父权限信息")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<Permission> getPermissionDetail(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        Permission permissionDetail = permissionService.getPermissionDetail(id);
        return Result.success(permissionDetail);
    }

    /**
     * 检查权限是否被使用
     */
    @GetMapping("/{id}/in-use")
    @Operation(summary = "检查权限使用", description = "检查权限是否被角色或用户使用")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<Boolean> isPermissionInUse(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean inUse = permissionService.isPermissionInUse(id);
        return Result.success(inUse);
    }

    /**
     * 根据URL和方法查询权限
     */
    @GetMapping("/by-url")
    @Operation(summary = "根据URL查询权限", description = "根据URL和HTTP方法查询权限")
    @PreAuthorize("hasPermission('permission:detail')")
    public Result<Permission> getPermissionByUrl(
            @Parameter(description = "权限URL", required = true) @RequestParam @NotEmpty String permissionUrl,
            @Parameter(description = "HTTP方法") @RequestParam(required = false) String httpMethod) {
        
        Permission permission = permissionService.getPermissionByUrl(permissionUrl, httpMethod);
        return Result.success(permission);
    }

    /**
     * 获取用户菜单树
     */
    @GetMapping("/user-menus/{userId}")
    @Operation(summary = "获取用户菜单树", description = "获取用户的菜单权限树")
    @PreAuthorize("hasPermission('permission:list') or #userId == authentication.principal.id")
    public Result<List<Permission>> getUserMenuTree(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long userId) {
        
        List<Permission> menuTree = permissionService.getUserMenuTree(userId);
        return Result.success(menuTree);
    }

    /**
     * 获取用户按钮权限
     */
    @GetMapping("/user-buttons/{userId}")
    @Operation(summary = "获取用户按钮权限", description = "获取用户的按钮权限列表")
    @PreAuthorize("hasPermission('permission:list') or #userId == authentication.principal.id")
    public Result<List<String>> getUserButtonPermissions(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long userId) {
        
        List<String> buttonPermissions = permissionService.getUserButtonPermissions(userId);
        return Result.success(buttonPermissions);
    }

    /**
     * 获取用户API权限
     */
    @GetMapping("/user-apis/{userId}")
    @Operation(summary = "获取用户API权限", description = "获取用户的API权限列表")
    @PreAuthorize("hasPermission('permission:list') or #userId == authentication.principal.id")
    public Result<List<String>> getUserApiPermissions(
            @Parameter(description = "用户ID", required = true) @PathVariable @NotNull Long userId) {
        
        List<String> apiPermissions = permissionService.getUserApiPermissions(userId);
        return Result.success(apiPermissions);
    }

    /**
     * 根据角色ID列表查询权限
     */
    @PostMapping("/by-roles")
    @Operation(summary = "根据角色列表查询权限", description = "根据角色ID列表查询权限")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getPermissionsByRoleIds(
            @Parameter(description = "角色ID列表", required = true) @RequestBody @NotEmpty List<Long> roleIds) {
        
        List<Permission> permissions = permissionService.getPermissionsByRoleIds(roleIds);
        return Result.success(permissions);
    }

    /**
     * 获取系统权限列表
     */
    @GetMapping("/system")
    @Operation(summary = "获取系统权限", description = "获取系统级权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getSystemPermissions() {
        
        List<Permission> systemPermissions = permissionService.getSystemPermissions();
        return Result.success(systemPermissions);
    }

    /**
     * 获取业务权限列表
     */
    @GetMapping("/business")
    @Operation(summary = "获取业务权限", description = "获取业务级权限列表")
    @PreAuthorize("hasPermission('permission:list')")
    public Result<List<Permission>> getBusinessPermissions() {
        
        List<Permission> businessPermissions = permissionService.getBusinessPermissions();
        return Result.success(businessPermissions);
    }

    /**
     * 更新权限路径
     */
    @PutMapping("/{id}/update-path")
    @Operation(summary = "更新权限路径", description = "更新权限及其子权限的路径")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Void> updatePermissionPath(
            @Parameter(description = "权限ID", required = true) @PathVariable @NotNull Long id) {
        
        boolean updated = permissionService.updatePermissionPath(id);
        return updated ? Result.success() : Result.error("路径更新失败");
    }

    /**
     * 批量更新子权限路径
     */
    @PutMapping("/batch-update-child-paths")
    @Operation(summary = "批量更新子权限路径", description = "批量更新子权限的路径")
    @PreAuthorize("hasPermission('permission:update')")
    public Result<Void> batchUpdateChildPaths(
            @Parameter(description = "父权限ID", required = true) @RequestParam @NotNull Long parentId,
            @Parameter(description = "新的父路径", required = true) @RequestParam @NotEmpty String newParentPath) {
        
        boolean updated = permissionService.batchUpdateChildPaths(parentId, newParentPath);
        return updated ? Result.success() : Result.error("批量更新失败");
    }
}