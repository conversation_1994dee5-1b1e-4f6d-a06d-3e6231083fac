package com.kingdee.integration.common.page;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分页响应结果类
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "分页响应结果")
public class PageResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页码
     */
    @Schema(description = "当前页码", example = "1")
    private Integer pageNum;

    /**
     * 每页大小
     */
    @Schema(description = "每页大小", example = "10")
    private Integer pageSize;

    /**
     * 总记录数
     */
    @Schema(description = "总记录数", example = "100")
    private Long total;

    /**
     * 总页数
     */
    @Schema(description = "总页数", example = "10")
    private Integer pages;

    /**
     * 数据列表
     */
    @Schema(description = "数据列表")
    private List<T> records;

    /**
     * 是否有上一页
     */
    @Schema(description = "是否有上一页", example = "false")
    private Boolean hasPrevious;

    /**
     * 是否有下一页
     */
    @Schema(description = "是否有下一页", example = "true")
    private Boolean hasNext;

    /**
     * 是否为第一页
     */
    @Schema(description = "是否为第一页", example = "true")
    private Boolean isFirst;

    /**
     * 是否为最后一页
     */
    @Schema(description = "是否为最后一页", example = "false")
    private Boolean isLast;

    /**
     * 是否为空页
     */
    @Schema(description = "是否为空页", example = "false")
    private Boolean isEmpty;

    /**
     * 排序信息
     */
    @Schema(description = "排序信息")
    private String orderBy;

    public PageResult() {
    }

    public PageResult(List<T> records, Integer pageNum, Integer pageSize, Long total) {
        this.records = records;
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.calculatePages();
        this.calculateFlags();
    }

    /**
     * 计算总页数
     */
    private void calculatePages() {
        if (total != null && pageSize != null && pageSize > 0) {
            this.pages = (int) Math.ceil((double) total / pageSize);
        } else {
            this.pages = 0;
        }
    }

    /**
     * 计算分页标志
     */
    private void calculateFlags() {
        if (pageNum != null && pages != null) {
            this.hasPrevious = pageNum > 1;
            this.hasNext = pageNum < pages;
            this.isFirst = pageNum == 1;
            this.isLast = pageNum.equals(pages) || pages == 0;
        } else {
            this.hasPrevious = false;
            this.hasNext = false;
            this.isFirst = true;
            this.isLast = true;
        }
        this.isEmpty = records == null || records.isEmpty();
    }

    /**
     * 设置总记录数并重新计算分页信息
     * 
     * @param total 总记录数
     * @return 当前对象
     */
    public PageResult<T> setTotal(Long total) {
        this.total = total;
        this.calculatePages();
        this.calculateFlags();
        return this;
    }

    /**
     * 设置页码并重新计算分页信息
     * 
     * @param pageNum 页码
     * @return 当前对象
     */
    public PageResult<T> setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
        this.calculateFlags();
        return this;
    }

    /**
     * 设置每页大小并重新计算分页信息
     * 
     * @param pageSize 每页大小
     * @return 当前对象
     */
    public PageResult<T> setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
        this.calculatePages();
        this.calculateFlags();
        return this;
    }

    /**
     * 设置数据列表并重新计算分页信息
     * 
     * @param records 数据列表
     * @return 当前对象
     */
    public PageResult<T> setRecords(List<T> records) {
        this.records = records;
        this.calculateFlags();
        return this;
    }

    /**
     * 获取数据列表大小
     * 
     * @return 数据列表大小
     */
    public int getSize() {
        return records != null ? records.size() : 0;
    }

    /**
     * 获取起始记录索引（从0开始）
     * 
     * @return 起始记录索引
     */
    public int getStartIndex() {
        if (pageNum != null && pageSize != null && pageNum > 0) {
            return (pageNum - 1) * pageSize;
        }
        return 0;
    }

    /**
     * 获取结束记录索引（从0开始）
     * 
     * @return 结束记录索引
     */
    public int getEndIndex() {
        int startIndex = getStartIndex();
        int size = getSize();
        return startIndex + size - 1;
    }

    /**
     * 转换数据类型
     * 
     * @param mapper 转换函数
     * @param <R> 目标类型
     * @return 转换后的分页结果
     */
    public <R> PageResult<R> map(Function<T, R> mapper) {
        List<R> convertedRecords = records != null ? 
            records.stream().map(mapper).collect(Collectors.toList()) : 
            Collections.emptyList();
        
        PageResult<R> result = new PageResult<>();
        result.setPageNum(this.pageNum)
              .setPageSize(this.pageSize)
              .setTotal(this.total)
              .setRecords(convertedRecords)
              .setOrderBy(this.orderBy);
        
        return result;
    }

    /**
     * 创建空的分页结果
     * 
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty() {
        return new PageResult<>(Collections.emptyList(), 1, 10, 0L);
    }

    /**
     * 创建空的分页结果
     * 
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param <T> 数据类型
     * @return 空的分页结果
     */
    public static <T> PageResult<T> empty(Integer pageNum, Integer pageSize) {
        return new PageResult<>(Collections.emptyList(), pageNum, pageSize, 0L);
    }

    /**
     * 创建分页结果
     * 
     * @param records 数据列表
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @param total 总记录数
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, Integer pageNum, Integer pageSize, Long total) {
        return new PageResult<>(records, pageNum, pageSize, total);
    }

    /**
     * 创建分页结果
     * 
     * @param records 数据列表
     * @param pageRequest 分页请求
     * @param total 总记录数
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(List<T> records, PageRequest pageRequest, Long total) {
        PageResult<T> result = new PageResult<>(records, pageRequest.getPageNum(), pageRequest.getPageSize(), total);
        result.setOrderBy(pageRequest.getOrderBySql());
        return result;
    }

    /**
     * 从MyBatis Plus的Page对象创建分页结果
     * 
     * @param page MyBatis Plus的Page对象
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> fromPage(com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page) {
        return new PageResult<>(
            page.getRecords(),
            (int) page.getCurrent(),
            (int) page.getSize(),
            page.getTotal()
        );
    }

    /**
     * 转换为MyBatis Plus的Page对象
     * 
     * @return MyBatis Plus的Page对象
     */
    public com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> toPage() {
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<T> page = 
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageNum, pageSize);
        page.setRecords(records);
        page.setTotal(total);
        return page;
    }

    /**
     * 获取分页信息摘要
     * 
     * @return 分页信息摘要
     */
    public String getSummary() {
        if (total == null || total == 0) {
            return "暂无数据";
        }
        
        int startIndex = getStartIndex() + 1;
        int endIndex = Math.min(getStartIndex() + getSize(), total.intValue());
        
        return String.format("第 %d-%d 条，共 %d 条记录，第 %d/%d 页", 
                           startIndex, endIndex, total, pageNum, pages);
    }

    @Override
    public String toString() {
        return "PageResult{" +
                "pageNum=" + pageNum +
                ", pageSize=" + pageSize +
                ", total=" + total +
                ", pages=" + pages +
                ", recordsSize=" + getSize() +
                ", hasPrevious=" + hasPrevious +
                ", hasNext=" + hasNext +
                ", isEmpty=" + isEmpty +
                '}';
    }
}